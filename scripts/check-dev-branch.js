#!/usr/bin/env node

console.log('Running check-dev-branch script...');

const { execSync } = require('child_process');

// Get the hook type from environment variable
const hookType = process.env.HOOK_TYPE || 'unknown';

// Function to execute git commands and return output
function git(command) {
  try {
    return execSync(`git ${command}`, { encoding: 'utf8' }).trim();
  } catch (error) {
    console.error(`Error executing git command: ${command}`);
    console.error(error.message);
    process.exit(1);
  }
}

// Get current branch
const currentBranch = git('rev-parse --abbrev-ref HEAD');

// Skip if we're on dev branch
if (currentBranch === 'dev') {
  process.exit(0);
}

// Fetch latest changes from remote
try {
  git('fetch origin dev:dev');
} catch (error) {
  console.error('Failed to fetch latest changes from dev branch.');
  console.error('Please check your internet connection and try again.');
  process.exit(1);
}

// Get the latest commit hash from dev
const remoteCommit = git('rev-parse dev');

// Get the merge-base (common ancestor) of current branch and dev
const baseCommit = git(`merge-base ${currentBranch} dev`);

// Check if dev has new commits
if (remoteCommit !== baseCommit) {
  console.error('\x1b[31m%s\x1b[0m', `ERROR: Your branch is not up to date with the latest changes from dev.`);
  console.error('\x1b[31m%s\x1b[0m', `Please run 'git pull origin dev' to update your branch before ${hookType}.`);
  process.exit(1);
}

console.log('\x1b[32m%s\x1b[0m', `✓ Branch is up to date with dev. Proceeding with ${hookType}...`);
process.exit(0);