import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthGuard, RolesGuard } from './auth.guard';
import { JwtHelperService } from '../jwt-helper/jwt-helper.service';
import { RepositoryService } from '../repositories/repository.service';
import { user_states, UserRole } from '../db/schema';

describe('AuthGuard', () => {
  let guard: AuthGuard;
  let jwtHelperService: JwtHelperService;
  let repositoryService: RepositoryService;
  let reflector: Reflector;

  const mockJwtHelperService = {
    verifyAccessToken: jest.fn(),
  };

  const mockRepositoryService = {
    getUserByKey: jest.fn(),
  };

  const mockReflector = {
    getAllAndOverride: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthGuard,
        {
          provide: JwtHelperService,
          useValue: mockJwtHelperService,
        },
        {
          provide: RepositoryService,
          useValue: mockRepositoryService,
        },
        {
          provide: Reflector,
          useValue: mockReflector,
        },
      ],
    }).compile();

    guard = module.get<AuthGuard>(AuthGuard);
    jwtHelperService = module.get<JwtHelperService>(JwtHelperService);
    repositoryService = module.get<RepositoryService>(RepositoryService);
    reflector = module.get<Reflector>(Reflector);

    jest.clearAllMocks();
  });

  const createMockExecutionContext = (headers: any = {}): ExecutionContext => {
    const mockRequest = {
      headers,
      user: undefined,
    };

    return {
      switchToHttp: () => ({
        getRequest: () => mockRequest,
      }),
      getHandler: jest.fn(),
      getClass: jest.fn(),
    } as any;
  };

  describe('canActivate', () => {
    it('should allow access to public routes', async () => {
      const context = createMockExecutionContext();
      mockReflector.getAllAndOverride.mockReturnValue(true); // IS_PUBLIC_KEY = true

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
      expect(mockReflector.getAllAndOverride).toHaveBeenCalledWith('isPublic', [
        context.getHandler(),
        context.getClass(),
      ]);
    });

    it('should authenticate user with valid token', async () => {
      const context = createMockExecutionContext({
        authorization: 'Bearer valid-token',
      });
      const mockUser = {
        id: 'user-123',
        role: UserRole.STUDENT,
        state: user_states.ACTIVE,
      };
      const mockPayload = { userId: 'user-123' };

      mockReflector.getAllAndOverride.mockReturnValue(false); // not public
      mockJwtHelperService.verifyAccessToken.mockResolvedValue(mockPayload);
      mockRepositoryService.getUserByKey.mockResolvedValue(mockUser);

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
      expect(mockJwtHelperService.verifyAccessToken).toHaveBeenCalledWith({
        token: 'valid-token',
      });
      expect(mockRepositoryService.getUserByKey).toHaveBeenCalledWith('id', 'user-123');
    });

    it('should authenticate user with verified state', async () => {
      const context = createMockExecutionContext({
        authorization: 'Bearer valid-token',
      });
      const mockUser = {
        id: 'user-123',
        role: UserRole.STUDENT,
        state: user_states.VERIFIED,
      };
      const mockPayload = { userId: 'user-123' };

      mockReflector.getAllAndOverride.mockReturnValue(false);
      mockJwtHelperService.verifyAccessToken.mockResolvedValue(mockPayload);
      mockRepositoryService.getUserByKey.mockResolvedValue(mockUser);

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
    });

    it('should throw UnauthorizedException if no authorization header', async () => {
      const context = createMockExecutionContext();
      mockReflector.getAllAndOverride.mockReturnValue(false); // not public

      await expect(guard.canActivate(context)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException if token verification fails', async () => {
      const context = createMockExecutionContext({
        authorization: 'Bearer invalid-token',
      });

      mockReflector.getAllAndOverride.mockReturnValue(false);
      mockJwtHelperService.verifyAccessToken.mockRejectedValue(new Error('Invalid token'));

      await expect(guard.canActivate(context)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException if user not found', async () => {
      const context = createMockExecutionContext({
        authorization: 'Bearer valid-token',
      });
      const mockPayload = { userId: 'user-123' };

      mockReflector.getAllAndOverride.mockReturnValue(false);
      mockJwtHelperService.verifyAccessToken.mockResolvedValue(mockPayload);
      mockRepositoryService.getUserByKey.mockResolvedValue(null);

      await expect(guard.canActivate(context)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException if user is not active or verified', async () => {
      const context = createMockExecutionContext({
        authorization: 'Bearer valid-token',
      });
      const mockUser = {
        id: 'user-123',
        role: UserRole.STUDENT,
        state: user_states.PENDING,
      };
      const mockPayload = { userId: 'user-123' };

      mockReflector.getAllAndOverride.mockReturnValue(false);
      mockJwtHelperService.verifyAccessToken.mockResolvedValue(mockPayload);
      mockRepositoryService.getUserByKey.mockResolvedValue(mockUser);

      await expect(guard.canActivate(context)).rejects.toThrow(UnauthorizedException);
    });
  });
});

describe('RolesGuard', () => {
  let guard: RolesGuard;
  let reflector: Reflector;

  const mockReflector = {
    getAllAndOverride: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RolesGuard,
        {
          provide: Reflector,
          useValue: mockReflector,
        },
      ],
    }).compile();

    guard = module.get<RolesGuard>(RolesGuard);
    reflector = module.get<Reflector>(Reflector);

    jest.clearAllMocks();
  });

  const createMockExecutionContext = (user: any = {}): ExecutionContext => {
    const mockRequest = { user };

    return {
      switchToHttp: () => ({
        getRequest: () => mockRequest,
      }),
      getHandler: jest.fn(),
      getClass: jest.fn(),
    } as any;
  };

  describe('canActivate', () => {
    it('should allow access if no roles are required', async () => {
      const context = createMockExecutionContext();
      mockReflector.getAllAndOverride.mockReturnValue(null); // no roles required

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
    });

    it('should allow access if user has required role', async () => {
      const context = createMockExecutionContext({ role: UserRole.ADMIN });
      mockReflector.getAllAndOverride.mockReturnValue([UserRole.ADMIN, UserRole.STUDENT]);

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
    });

    it('should deny access if user does not have required role', async () => {
      const context = createMockExecutionContext({ role: UserRole.STUDENT });
      mockReflector.getAllAndOverride.mockReturnValue([UserRole.ADMIN]);

      const result = await guard.canActivate(context);

      expect(result).toBe(false);
    });

    it('should allow access if user has one of multiple required roles', async () => {
      const context = createMockExecutionContext({ role: UserRole.STUDENT });
      mockReflector.getAllAndOverride.mockReturnValue([UserRole.ADMIN, UserRole.STUDENT]);

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
    });
  });
});
