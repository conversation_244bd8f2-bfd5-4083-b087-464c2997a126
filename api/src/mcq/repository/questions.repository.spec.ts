import { Test, TestingModule } from '@nestjs/testing';
import { QuestionRepository } from './questions.repository';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { ConflictException, NotFoundException } from '@nestjs/common';
import { questionsSchema } from '@/db/schema/questions';

describe('QuestionRepository', () => {
  let repository: QuestionRepository;
  let mockDrizzleService: any;

  beforeEach(async () => {
    mockDrizzleService = {
      db: {
        insert: jest.fn(),
        select: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QuestionRepository,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
      ],
    }).compile();

    repository = module.get<QuestionRepository>(QuestionRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createQuestion', () => {
    it('should create a single question successfully', async () => {
      const questionData = {
        question: 'What is 2+2?',
        option_a: '3',
        option_b: '4',
        option_c: '5',
        option_d: '6',
        answers: 'B',
        questionBank_id: 'bank-1',
      };
      const mockResult = [{ id: 'question-1', ...questionData }];

      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      const result = await repository.createQuestion(questionData);

      expect(mockDrizzleService.db.insert).toHaveBeenCalledWith(
        questionsSchema,
      );
      expect(mockInsertQuery.values).toHaveBeenCalledWith(questionData);
      expect(mockInsertQuery.returning).toHaveBeenCalled();
      expect(result).toEqual(mockResult);
    });

    it('should create multiple questions successfully', async () => {
      const questionsData = [
        {
          question: 'What is 2+2?',
          option_a: '3',
          option_b: '4',
          option_c: '5',
          option_d: '6',
          answers: 'B',
          questionBank_id: 'bank-1',
        },
        {
          question: 'What is 3+3?',
          option_a: '5',
          option_b: '6',
          option_c: '7',
          option_d: '8',
          answers: 'B',
          questionBank_id: 'bank-1',
        },
      ];
      const mockResult = questionsData.map((q, i) => ({
        id: `question-${i + 1}`,
        ...q,
      }));

      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      const result = await repository.createQuestion(questionsData);

      expect(mockDrizzleService.db.insert).toHaveBeenCalledWith(
        questionsSchema,
      );
      expect(mockInsertQuery.values).toHaveBeenCalledWith(questionsData);
      expect(result).toEqual(mockResult);
    });

    it('should throw ConflictException for duplicate questions', async () => {
      const questionData = { question: 'Duplicate question' };
      const mockError = {
        code: '23505', // Unique constraint violation
        message: 'duplicate key value violates unique constraint',
      };

      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockRejectedValue(mockError),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      await expect(repository.createQuestion(questionData)).rejects.toThrow(
        ConflictException,
      );
    });

    it('should throw NotFoundException for invalid foreign key reference', async () => {
      const questionData = { questionBank_id: 'invalid-bank-id' };
      const mockError = {
        code: '23503', // Foreign key violation
        message: 'foreign key constraint violation',
      };

      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockRejectedValue(mockError),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      await expect(repository.createQuestion(questionData)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should rethrow other database errors', async () => {
      const questionData = { question: 'Test question' };
      const mockError = new Error('Unexpected database error');

      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockRejectedValue(mockError),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      await expect(repository.createQuestion(questionData)).rejects.toThrow(
        'Unexpected database error',
      );
    });
  });

  describe('getQuiz', () => {
    it('should return random questions for quiz', async () => {
      const questionBankId = 'bank-1';
      const totalQuestions = 5;
      const mockQuestions = Array.from({ length: 5 }, (_, i) => ({
        id: `question-${i + 1}`,
        question: `Question ${i + 1}`,
        option_a: 'A',
        option_b: 'B',
        option_c: 'C',
        option_d: 'D',
        answers: 'A',
      }));

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue(mockQuestions),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const result = await repository.getQuiz(questionBankId, totalQuestions);

      expect(mockDrizzleService.db.select).toHaveBeenCalled();
      expect(mockQuery.from).toHaveBeenCalledWith(questionsSchema);
      expect(mockQuery.where).toHaveBeenCalled();
      expect(mockQuery.orderBy).toHaveBeenCalled();
      expect(mockQuery.limit).toHaveBeenCalledWith(totalQuestions);
      expect(result).toEqual(mockQuestions);
    });

    it('should throw NotFoundException when no questions available', async () => {
      const questionBankId = 'empty-bank';
      const totalQuestions = 5;

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue([]),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      await expect(
        repository.getQuiz(questionBankId, totalQuestions),
      ).rejects.toThrow(NotFoundException);
    });

    it('should use custom transaction when provided', async () => {
      const questionBankId = 'bank-1';
      const totalQuestions = 3;
      const mockQuestions = [{ id: 'q1' }, { id: 'q2' }, { id: 'q3' }];
      const mockTx = {
        select: jest.fn(),
      };

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue(mockQuestions),
      };
      mockTx.select.mockReturnValue(mockQuery);

      const result = await repository.getQuiz(
        questionBankId,
        totalQuestions,
        mockTx,
      );

      expect(mockTx.select).toHaveBeenCalled();
      expect(result).toEqual(mockQuestions);
    });
  });

  describe('getGoldenQuestions', () => {
    it('should return golden questions successfully', async () => {
      const questionBankId = 'bank-1';
      const total = 3;
      const mockGoldenQuestions = Array.from({ length: 3 }, (_, i) => ({
        id: `golden-${i + 1}`,
        question: `Golden Question ${i + 1}`,
        option_a: 'A',
        option_b: 'B',
        option_c: 'C',
        option_d: 'D',
        answers: 'A',
      }));

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue(mockGoldenQuestions),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const result = await repository.getGoldenQuestions(questionBankId, total);

      expect(mockDrizzleService.db.select).toHaveBeenCalled();
      expect(mockQuery.from).toHaveBeenCalledWith(questionsSchema);
      expect(mockQuery.where).toHaveBeenCalled();
      expect(mockQuery.orderBy).toHaveBeenCalled();
      expect(mockQuery.limit).toHaveBeenCalledWith(total);
      expect(result).toEqual(mockGoldenQuestions);
    });

    it('should throw NotFoundException when no golden questions available', async () => {
      const questionBankId = 'bank-without-golden';
      const total = 2;

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue([]),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      await expect(
        repository.getGoldenQuestions(questionBankId, total),
      ).rejects.toThrow(NotFoundException);
      await expect(
        repository.getGoldenQuestions(questionBankId, total),
      ).rejects.toThrow('No golden questions available for this question bank');
    });

    it('should use custom transaction when provided', async () => {
      const questionBankId = 'bank-1';
      const total = 2;
      const mockQuestions = [{ id: 'golden-1' }, { id: 'golden-2' }];
      const mockTx = {
        select: jest.fn(),
      };

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue(mockQuestions),
      };
      mockTx.select.mockReturnValue(mockQuery);

      const result = await repository.getGoldenQuestions(
        questionBankId,
        total,
        mockTx,
      );

      expect(mockTx.select).toHaveBeenCalled();
      expect(result).toEqual(mockQuestions);
    });
  });

  describe('deleteQuestion', () => {
    it('should delete question successfully', async () => {
      const questionId = 'question-1';
      const mockResult = [{ id: questionId }];

      const mockDeleteQuery = {
        where: jest.fn().mockReturnThis(),
        returning: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.delete.mockReturnValue(mockDeleteQuery);

      const result = await repository.deleteQuestion(questionId);

      expect(mockDrizzleService.db.delete).toHaveBeenCalledWith(
        questionsSchema,
      );
      expect(mockDeleteQuery.where).toHaveBeenCalled();
      expect(mockDeleteQuery.returning).toHaveBeenCalled();
      expect(result).toEqual({
        message: 'Question deleted successfully',
        statusCode: 200,
      });
    });

    it('should throw NotFoundException when question not found for deletion', async () => {
      const questionId = 'non-existent';

      const mockDeleteQuery = {
        where: jest.fn().mockReturnThis(),
        returning: jest.fn().mockResolvedValue([]),
      };
      mockDrizzleService.db.delete.mockReturnValue(mockDeleteQuery);

      await expect(repository.deleteQuestion(questionId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});
