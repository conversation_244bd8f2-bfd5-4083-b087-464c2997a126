import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { McqRepository } from '../repository/mcq.repository';
import {
  createQuestionBankDto,
  updateQuestionBankDto,
} from '../dto/question-bank.dto';
import { organisations, User, user_roles, users } from '@/db/schema';
import { PaginationParams } from '@/common/interfaces/pagination.interface';
import { QuestionBank, questionBank } from '@/db/schema/mcq';
import {
  and,
  asc,
  desc,
  eq,
  getTableColumns,
  ilike,
  ne,
  or,
  sql,
} from 'drizzle-orm';
import { RepositoryService } from '@/repositories/repository.service';
import { alias } from 'drizzle-orm/pg-core';
import { MCQ_Messages } from '@app/shared/constants/mcq.constants';

@Injectable()
export class QuestionBankService {
  private readonly logger = new Logger(QuestionBankService.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly mcqRepository: McqRepository,
    private readonly repositoryService: RepositoryService,
  ) {}

  async createQuestionBank(
    data: createQuestionBankDto,
    imageUrl: string,
    user: User,
  ) {
    const createdQuestionBank = await this.mcqRepository.createQuestionBank({
      ...data,
      createdBy: user.id,
      imageUrl,
    });
    return createdQuestionBank;
  }

  async getQuestionBanks({
    sort = 'id',
    order = 'asc',
    limit = 10,
    search = '',
    page = 1,
  }: PaginationParams & {
    sort: keyof QuestionBank;
  }) {
    const where = [];
    if (search) {
      const searchPattern = `'%${search.toString().toLocaleLowerCase()}%'`;
      where.push(
        sql`LOWER(${questionBank.name}) LIKE ${sql.raw(searchPattern)} OR LOWER(${questionBank.stack}) LIKE ${sql.raw(searchPattern)} OR LOWER(${questionBank.framework}) LIKE ${sql.raw(searchPattern)}`,
      );
    }

    const questionBanks = await this.drizzle.db
      .select({
        ...getTableColumns(questionBank),
        created_by: {
          id: users.id,
          email: users.email,
          name: organisations.name,
        },
      })
      .from(questionBank)
      .where(and(...where))
      .orderBy(
        order === 'asc' ? asc(questionBank[sort]) : desc(questionBank[sort]),
      )
      .leftJoin(users, eq(users.id, questionBank.createdBy))
      .leftJoin(organisations, eq(organisations.user_id, users.id))
      .limit(limit)
      .offset((page - 1) * limit);

    const total = await this.drizzle.db.$count(questionBank, and(...where));

    return {
      data: questionBanks,
      total: total,
    };
  }

  async getQuestionBank(id: string) {
    const creator = alias(users, 'createdBy');
    const [selectedQuestionBank] = await this.drizzle.db
      .select()
      .from(questionBank)
      .leftJoin(creator, eq(creator.id, questionBank.createdBy))
      .where(eq(questionBank.id, id));
    return selectedQuestionBank;
  }

  async updateQuestionBank(
    data: updateQuestionBankDto,
    id: string,
    user: User,
  ) {
    const questionBankToUpdate =
      await this.repositoryService.getModelByKey<QuestionBank>(
        questionBank,
        'id',
        id,
      );
    if (!questionBankToUpdate)
      throw new NotFoundException('Question bank not found');

    if (
      questionBankToUpdate.createdBy !== user.id &&
      user.role !== user_roles.SUPER_ADMIN
    )
      throw new UnauthorizedException(
        'You are not authorized to update this question bank',
      );

    const where = [];
    if (data.name)
      where.push(
        and(ilike(questionBank.name, data.name), ne(questionBank.id, id)),
      );
    if (data.framework)
      where.push(
        and(
          ilike(questionBank.framework, data.framework),
          ne(questionBank.id, id),
        ),
      );

    const [questionBankExists] = await this.drizzle.db
      .select()
      .from(questionBank)
      .where(and(or(...where), ne(questionBank.id, id)));

    if (questionBankExists)
      throw new ConflictException(MCQ_Messages.Question_Bank_Already_Exists);

    const updatedQuestionBank = await this.mcqRepository.updateQuestionBank(
      data,
      id,
    );
    return updatedQuestionBank;
  }

  async deleteQuestionBank(id: string, user: User) {
    const questionBankToDelete =
      await this.repositoryService.getModelByKey<QuestionBank>(
        questionBank,
        'id',
        id,
      );

    if (!questionBankToDelete) {
      throw new NotFoundException('Question bank not found');
    }

    if (
      questionBankToDelete.createdBy !== user.id &&
      user.role !== user_roles.SUPER_ADMIN
    ) {
      throw new UnauthorizedException(
        'You are not authorized to delete this question bank',
      );
    }
    await this.mcqRepository.deleteQuestionBank(id);
  }
}
