import { Injectable, Logger } from '@nestjs/common';
import { CacheService } from '@app/shared/cache/cache.service';
import { CacheConfigService } from '@app/shared/cache/cache-config.service';
import { Cacheable } from '@app/shared/cache/decorators/cacheable.decorator';
import { CacheInvalidate } from '@app/shared/cache/decorators/cache-invalidate.decorator';
import {
  CACHE_PREFIXES,
  CACHE_TTL,
} from '@app/shared/constants/cache.constant';

interface ExampleData {
  id: string;
  name: string;
  value: number;
  timestamp: number;
}

@Injectable()
export class CacheExampleService {
  private readonly logger = new Logger(CacheExampleService.name);
  private readonly data: Map<string, ExampleData> = new Map();

  constructor(
    private readonly cacheService: CacheService,
    private readonly cacheConfigService: CacheConfigService,
  ) {
    // Seed some example data
    this.data.set('1', {
      id: '1',
      name: 'Example 1',
      value: 100,
      timestamp: Date.now(),
    });
    this.data.set('2', {
      id: '2',
      name: 'Example 2',
      value: 200,
      timestamp: Date.now(),
    });
    this.data.set('3', {
      id: '3',
      name: 'Example 3',
      value: 300,
      timestamp: Date.now(),
    });
  }

  /**
   * Get all examples (cached for 1 minute)
   */
  @Cacheable({
    prefix: CACHE_PREFIXES.EXAMPLE,
    ttl: CACHE_TTL.ONE_MINUTE,
    keyGenerator: () => ['all'],
  })
  async getAllExamples(): Promise<ExampleData[]> {
    this.logger.log('Getting all examples from source (not cache)');
    // Simulate database query delay
    await new Promise((resolve) => setTimeout(resolve, 500));
    return Array.from(this.data.values());
  }

  /**
   * Get example by ID (cached for 5 minutes)
   */
  @Cacheable({
    prefix: CACHE_PREFIXES.EXAMPLE,
    ttl: CACHE_TTL.FIVE_MINUTES,
    keyGenerator: (args) => [args[0]], // args[0] is the id
    condition: (args) => !!args[0], // Only cache if ID is provided
  })
  async getExampleById(id: string): Promise<ExampleData | null> {
    this.logger.log(`Getting example ${id} from source (not cache)`);
    // Simulate database query delay
    await new Promise((resolve) => setTimeout(resolve, 300));
    return this.data.get(id) ?? null;
  }

  /**
   * Create a new example (invalidates cache)
   */
  @CacheInvalidate({
    prefix: CACHE_PREFIXES.EXAMPLE,
    keys: ['all'], // Invalidate the 'all' cache
    afterExecution: true, // Invalidate after successful execution
  })
  async createExample(name: string, value: number): Promise<ExampleData> {
    this.logger.log(`Creating new example: ${name}`);

    // Generate a new ID
    const id = (this.data.size + 1).toString();

    // Create the new example
    const newExample: ExampleData = {
      id,
      name,
      value,
      timestamp: Date.now(),
    };

    // Save to "database"
    this.data.set(id, newExample);

    return newExample;
  }

  /**
   * Update an example (invalidates specific caches)
   */
  @CacheInvalidate({
    prefix: CACHE_PREFIXES.EXAMPLE,
    keys: (args) => ['all', args[0]], // Invalidate both 'all' and the specific ID
    afterExecution: true,
  })
  async updateExample(
    id: string,
    updates: Partial<ExampleData>,
  ): Promise<ExampleData | null> {
    this.logger.log(`Updating example ${id}`);

    // Get the existing example
    const existing = this.data.get(id);
    if (!existing) {
      return null;
    }

    // Update the example
    const updated = {
      ...existing,
      ...updates,
      id, // Ensure ID doesn't change
      timestamp: Date.now(), // Update timestamp
    };

    // Save to "database"
    this.data.set(id, updated);

    return updated;
  }

  /**
   * Delete an example (invalidates specific caches)
   */
  @CacheInvalidate({
    prefix: CACHE_PREFIXES.EXAMPLE,
    keys: (args) => ['all', args[0]], // Invalidate both 'all' and the specific ID
    afterExecution: true,
  })
  async deleteExample(id: string): Promise<boolean> {
    this.logger.log(`Deleting example ${id}`);

    // Delete from "database"
    return this.data.delete(id);
  }

  /**
   * Manual cache example - demonstrates manual cache management
   */
  async getWithManualCache(id: string): Promise<ExampleData | null> {
    // Generate cache key
    const cacheKey = this.cacheService.generateKey(id, CACHE_PREFIXES.EXAMPLE);

    // Try to get from cache
    const cached = await this.cacheService.get<ExampleData>(cacheKey);
    if (cached) {
      this.logger.log(`Got example ${id} from cache`);
      return cached;
    }

    // Get from source
    this.logger.log(`Getting example ${id} from source (manual cache)`);
    const example = this.data.get(id) ?? null;

    // Cache the result if found
    if (example) {
      await this.cacheService.set(cacheKey, example, CACHE_TTL.FIVE_MINUTES);
    }

    return example;
  }
}
