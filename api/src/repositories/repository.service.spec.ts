import { Test, TestingModule } from '@nestjs/testing';
import { RepositoryService } from './repository.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { BadRequestException } from '@nestjs/common';
import { users, student_profiles } from '@/db/schema';

describe('RepositoryService', () => {
  let service: RepositoryService;
  let mockDrizzleService: any;

  beforeEach(async () => {
    mockDrizzleService = {
      db: {
        select: jest.fn(),
        delete: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RepositoryService,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
      ],
    }).compile();

    service = module.get<RepositoryService>(RepositoryService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getModelByKey', () => {
    it('should return model when found', async () => {
      const mockResult = [{ id: '1', name: 'Test User' }];
      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const result = await service.getModelByKey(users, 'id', '1');

      expect(result).toEqual(mockResult[0]);
      expect(mockDrizzleService.db.select).toHaveBeenCalled();
      expect(mockQuery.from).toHaveBeenCalledWith(users);
      expect(mockQuery.where).toHaveBeenCalled();
      expect(mockQuery.execute).toHaveBeenCalled();
    });

    it('should return null when model not found', async () => {
      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([]),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const result = await service.getModelByKey(users, 'id', 'non-existent');

      expect(result).toBeNull();
      expect(mockQuery.execute).toHaveBeenCalled();
    });

    it('should handle database errors', async () => {
      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockRejectedValue(new Error('Database error')),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      await expect(service.getModelByKey(users, 'id', '1')).rejects.toThrow(
        'Database error',
      );
    });
  });

  describe('getUserByKey', () => {
    it('should return user with student profile', async () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        student_profile: {
          id: 'profile-1',
          user_id: '1',
          first_name: 'John',
          last_name: 'Doe',
        },
      };
      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockResolvedValue([mockUser]),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const result = await service.getUserByKey('email', '<EMAIL>');

      expect(result).toEqual(mockUser);
      expect(mockDrizzleService.db.select).toHaveBeenCalled();
      expect(mockQuery.from).toHaveBeenCalledWith(users);
      expect(mockQuery.where).toHaveBeenCalled();
      expect(mockQuery.leftJoin).toHaveBeenCalled();
    });

    it('should return undefined when user not found', async () => {
      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockResolvedValue([]),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const result = await service.getUserByKey(
        'email',
        '<EMAIL>',
      );

      expect(result).toBeUndefined();
    });
  });

  describe('batchDelete', () => {
    it('should delete successfully when all IDs exist', async () => {
      const values = ['1', '2', '3'];

      // Mock findNonExistentIds to return empty array (all IDs exist)
      jest.spyOn(service, 'findNonExistentIds').mockResolvedValue([]);

      const mockDeleteQuery = {
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ rowCount: 3 }),
      };
      mockDrizzleService.db.delete.mockReturnValue(mockDeleteQuery);

      const result = await service.batchDelete(users, 'id', values);

      expect(service.findNonExistentIds).toHaveBeenCalledWith(
        users,
        values,
        'id',
      );
      expect(mockDrizzleService.db.delete).toHaveBeenCalledWith(users);
      expect(mockDeleteQuery.where).toHaveBeenCalled();
      expect(mockDeleteQuery.execute).toHaveBeenCalled();
      expect(result).toEqual({ rowCount: 3 });
    });

    it('should throw BadRequestException when some IDs do not exist', async () => {
      const values = ['1', '2', '3'];
      const nonExistentIds = ['2', '3'];

      jest
        .spyOn(service, 'findNonExistentIds')
        .mockResolvedValue(nonExistentIds);

      await expect(service.batchDelete(users, 'id', values)).rejects.toThrow(
        BadRequestException,
      );
      await expect(service.batchDelete(users, 'id', values)).rejects.toThrow(
        'Some IDs do not exist: 2, 3',
      );
    });

    it('should handle empty values array', async () => {
      const result = await service.batchDelete(users, 'id', []);

      expect(result).toBeUndefined();
      expect(mockDrizzleService.db.delete).not.toHaveBeenCalled();
    });

    it('should handle batch processing for large datasets', async () => {
      const values = Array.from({ length: 2500 }, (_, i) => `id-${i}`);

      jest.spyOn(service, 'findNonExistentIds').mockResolvedValue([]);

      const mockDeleteQuery = {
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ rowCount: 1000 }),
      };
      mockDrizzleService.db.delete.mockReturnValue(mockDeleteQuery);

      const result = await service.batchDelete(users, 'id', values, 1000);

      expect(service.findNonExistentIds).toHaveBeenCalledWith(
        users,
        values,
        'id',
      );
      // Should be called 3 times for 2500 items with batch size 1000
      expect(mockDrizzleService.db.delete).toHaveBeenCalledTimes(1); // Only first batch due to return
    });

    it('should handle database errors during deletion', async () => {
      const values = ['1', '2'];

      jest.spyOn(service, 'findNonExistentIds').mockResolvedValue([]);

      const mockDeleteQuery = {
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockRejectedValue(new Error('Database error')),
      };
      mockDrizzleService.db.delete.mockReturnValue(mockDeleteQuery);

      await expect(service.batchDelete(users, 'id', values)).rejects.toThrow(
        'Database error',
      );
    });
  });

  describe('findNonExistentIds', () => {
    it('should return empty array when all IDs exist', async () => {
      const ids = ['1', '2', '3'];
      const mockExistingIds = [{ id: '1' }, { id: '2' }, { id: '3' }];

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue(mockExistingIds),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const result = await service.findNonExistentIds(users, ids);

      expect(result).toEqual([]);
      expect(mockDrizzleService.db.select).toHaveBeenCalled();
    });

    it('should return non-existent IDs', async () => {
      const ids = ['1', '2', '3', '4'];
      const mockExistingIds = [{ id: '1' }, { id: '3' }];

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue(mockExistingIds),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const result = await service.findNonExistentIds(users, ids);

      expect(result).toEqual(['2', '4']);
    });

    it('should handle empty IDs array', async () => {
      const result = await service.findNonExistentIds(users, []);

      expect(result).toEqual([]);
      expect(mockDrizzleService.db.select).not.toHaveBeenCalled();
    });

    it('should handle custom ID column', async () => {
      const ids = ['email1', 'email2'];
      const mockExistingIds = [{ id: 'email1' }]; // The select always uses 'id' as alias

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue(mockExistingIds),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const result = await service.findNonExistentIds(users, ids, 'email');

      expect(result).toEqual(['email2']);
    });

    it('should handle numeric IDs', async () => {
      const ids = [1, 2, 3];
      const mockExistingIds = [{ id: 1 }, { id: 3 }];

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue(mockExistingIds),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const result = await service.findNonExistentIds(users, ids);

      expect(result).toEqual([2]);
    });
  });
});
