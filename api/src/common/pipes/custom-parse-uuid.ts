import {
  Parse<PERSON><PERSON>DPipe,
  BadRequestException,
  Optional,
  ArgumentMetadata,
} from '@nestjs/common';

export interface CustomParseUUIDPipeOptions {
  optional?: boolean;
  version?: '3' | '4' | '5' | undefined;
}

export class CustomParseUUIDPipe extends ParseUUIDPipe {
  private readonly optional: boolean;

  constructor(@Optional() options?: CustomParseUUIDPipeOptions) {
    super({
      version: options?.version,
      exceptionFactory: () =>
        new BadRequestException(
          'Validation failed: Invalid value in query or path',
        ),
    });
    this.optional = options?.optional ?? false;
  }

  override async transform(
    value: string,
    metadata: ArgumentMetadata,
  ): Promise<string> {
    // If the value is undefined or null and optional is true, return the value as is
    if (
      (value === undefined || value === null || value === '') &&
      this.optional
    ) {
      return value;
    }

    // Otherwise, use the parent class's transform method
    return super.transform(value, metadata);
  }
}
