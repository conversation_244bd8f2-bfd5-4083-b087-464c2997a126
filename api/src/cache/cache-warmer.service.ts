import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { CACHE_PREFIXES } from '@app/shared/constants/cache.constant';
import { ConfigService } from '@nestjs/config';
import { NotificationTypeService } from '@app/shared/notification/notification-type.service';
import { NotificationTemplateService } from '@app/shared/notification/notification-template.service';
import { SkillsService } from '@/skills/skills.service';
import { QuizService } from '@/mcq/quiz/quiz.service';

@Injectable()
export class CacheWarmerService implements OnModuleInit {
  private readonly logger = new Logger(CacheWarmerService.name);
  private readonly isEnabled: boolean;

  constructor(
    private readonly configService: ConfigService,
    private readonly notificationTypeService: NotificationTypeService,
    private readonly notificationTemplateService: NotificationTemplateService,
    private readonly skillsService: SkillsService,
    private readonly quizService: QuizService,
  ) {
    this.isEnabled =
      this.configService.get<string>('CACHE_WARMUP_ENABLED') === 'true';
  }

  async onModuleInit() {
    if (!this.isEnabled) {
      this.logger.log('Cache warmup is disabled');
      return;
    }

    try {
      // Delay cache warmup to allow application to fully initialize
      setTimeout(async () => {
        this.logger.log('Starting cache warmup process...');
        await this.warmCaches();
        this.logger.log('Cache warmup completed successfully');
      }, 5000); // 5 second delay
    } catch (error) {
      this.logger.error('Cache warmup failed', error);
    }
  }

  /**
   * Warms up all caches
   */
  async warmCaches() {
    const tasks = [this.warmReferenceData(), this.warmFrequentlyAccessedData()];

    await Promise.all(tasks);
  }

  private async warmReferenceData() {
    this.logger.debug('Warming reference data caches...');

    try {
      await this.warmNotificationTypesCache();
      await this.warmNotificationTemplatesCache();
      await this.warmSkillsCache();

      this.logger.debug('Reference data caches warmed successfully');
    } catch (error) {
      this.logger.warn('Failed to warm reference data caches', error);
    }
  }

  /**
   * Warms up skills cache
   */
  private async warmSkillsCache() {
    try {
      this.logger.debug('Warming skills cache...');
      await this.skillsService.getAllSkills({
        page: 1,
        limit: 10,
        sort: 'created_at',
        order: 'asc',
        search: '',
        all: false,
      });

      // Warm skill categories cache
      await this.skillsService.getAllSkillCategories();

      // Get a few individual skills to warm their caches too
      const skills = await this.skillsService.getAllSkills({
        page: 1,
        limit: 10,
        sort: 'created_at',
        order: 'asc',
        search: '',
        all: false,
      });

      if (skills && skills.length > 0) {
        // Warm cache for the first few skills
        const samplesToWarm = Math.min(skills.length, 3);
        for (let i = 0; i < samplesToWarm; i++) {
          await this.skillsService.getSkillById(skills[i]!.id);
        }
      }

      // Get skill categories and warm skills by category cache
      const categories = await this.skillsService.getAllSkillCategories();
      if (categories && categories.length > 0) {
        // Warm cache for the first few categories
        const categoriesToWarm = Math.min(categories.length, 3);
        for (let i = 0; i < categoriesToWarm; i++) {
          await this.skillsService.getSkillsByCategory(categories[i]!.id);
        }
      }

      this.logger.debug('Skills cache warmed successfully');
    } catch (error) {
      this.logger.warn('Failed to warm skills cache', error);
    }
  }

  /**
   * Warms up notification types cache
   */
  private async warmNotificationTypesCache() {
    try {
      this.logger.debug('Warming notification types cache...');
      await this.notificationTypeService.getNotificationTypes();
      this.logger.debug('Notification types cache warmed successfully');
    } catch (error) {
      this.logger.warn('Failed to warm notification types cache', error);
    }
  }

  /**
   * Warms up notification templates cache
   */
  private async warmNotificationTemplatesCache() {
    try {
      this.logger.debug('Warming notification templates cache...');
      await this.notificationTemplateService.getNotificationTemplates();
      this.logger.debug('Notification templates cache warmed successfully');
    } catch (error) {
      this.logger.warn('Failed to warm notification templates cache', error);
    }
  }

  private async warmFrequentlyAccessedData() {
    this.logger.debug('Warming frequently accessed data caches...');

    try {
      await this.warmNotificationHistoryCache();
      await this.warmQuizCache();

      this.logger.debug('Frequently accessed data caches warmed successfully');
    } catch (error) {
      this.logger.warn('Failed to warm frequently accessed data caches', error);
    }
  }

  private async warmQuizCache() {
    try {
      this.logger.debug('Warming quiz caches...');

      // Warm available quizzes cache
      const availableQuizzes = await this.quizService.getAvailableQuizzes();

      // Warm individual quiz caches for the first few available quizzes
      if (availableQuizzes && availableQuizzes.length > 0) {
        const samplesToWarm = Math.min(availableQuizzes.length, 5);
        for (let i = 0; i < samplesToWarm; i++) {
          await this.quizService.getQuizById(availableQuizzes[i].id);
        }
      }

      this.logger.debug('Quiz caches warmed successfully');
    } catch (error) {
      this.logger.warn('Failed to warm quiz caches', error);
    }
  }

  private async warmNotificationHistoryCache() {
    try {
      this.logger.debug(
        'Warming notification history cache for active users...',
      );
      this.logger.debug('Notification history cache warming completed');
    } catch (error) {
      this.logger.warn('Failed to warm notification history cache', error);
    }
  }

  /**
   * Manually trigger cache warming
   */
  async manualWarmup(entity?: string) {
    if (entity) {
      this.logger.log(`Manually warming cache for ${entity}`);

      // Switch based on entity type
      switch (entity) {
        case CACHE_PREFIXES.NOTIFICATION:
          await this.warmNotificationTypesCache();
          await this.warmNotificationTemplatesCache();
          await this.warmNotificationHistoryCache();
          break;
        case 'notification_types':
          await this.warmNotificationTypesCache();
          break;
        case 'notification_templates':
          await this.warmNotificationTemplatesCache();
          break;
        case 'notification_history':
          await this.warmNotificationHistoryCache();
          break;
        case CACHE_PREFIXES.SKILL:
          await this.warmSkillsCache();
          break;
        case CACHE_PREFIXES.SKILL_CATEGORY:
          await this.warmSkillsCache();
          break;
        case CACHE_PREFIXES.QUIZ:
          await this.warmQuizCache();
          break;
        // Add other entity types
        default:
          this.logger.warn(`No warmup implementation for ${entity}`);
      }
    } else {
      this.logger.log('Manually warming all caches');
      await this.warmCaches();
    }
  }
}
