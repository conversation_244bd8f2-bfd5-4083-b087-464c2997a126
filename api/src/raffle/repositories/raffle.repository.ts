import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import {
  eq,
  or,
  and,
  inArray,
  getTableColumns,
  count,
  sql,
  desc,
  asc,
  ilike,
  lte,
} from 'drizzle-orm';
import {
  participantSchema,
  raffleInstitutionsSchema,
  raffleSchema,
  winnersSchema,
} from '@/db/schema/raffle';

import {
  institutions,
  organisations,
  student_profiles,
  users,
} from '@/db/schema';
import { formatField } from '@/util/formatField';
import { RaffleData, RaffleFilter } from '../dto/index.dto';
import { randomInt } from 'node:crypto';
@Injectable()
export class RaffleRepository {
  constructor(private readonly drizzle: DrizzleService) {}
  private logger = new Logger(RaffleRepository.name);
  /**
   * Get a raffle by ID with all related data (complex query with joins)
   * @param id Raffle ID
   * @param tx Database transaction
   * @returns Raffle with related data
   */
  async getRaffleById(id: string, tx = this.drizzle.db): Promise<any> {
    try {
      const result = await tx
        .select({
          id: raffleSchema.id,
          name: raffleSchema.name,
          description: raffleSchema.description,
          startDate: raffleSchema.startDate,
          endDate: raffleSchema.endDate,
          maxParticipants: raffleSchema.maxParticipants,
          totalWinners: raffleSchema.totalWinners,
          institutionId: raffleSchema.institutionId,
          degree: sql`jsonb_agg(DISTINCT ${raffleSchema.degree}::jsonb)`.as(
            'degree',
          ),
          scheduled: raffleSchema.scheduled,
          programme:
            sql`jsonb_agg(DISTINCT ${raffleSchema.programme}::jsonb)`.as(
              'programme',
            ),

          level: raffleSchema.level,
          status: raffleSchema.status,
          created_at: raffleSchema.created_at,
          updated_at: raffleSchema.updated_at,
          institutions: sql`array_agg(DISTINCT ${institutions.name})`.as(
            'institutions',
          ),
        })
        .from(raffleSchema)
        .leftJoin(
          raffleInstitutionsSchema,
          eq(raffleSchema.id, raffleInstitutionsSchema.raffleId),
        )
        .leftJoin(
          institutions,
          eq(raffleInstitutionsSchema.institutionId, institutions.id),
        )
        .where(eq(raffleSchema.id, id))
        .groupBy(
          raffleSchema.id,
          raffleSchema.name,
          raffleSchema.description,
          raffleSchema.startDate,
          raffleSchema.endDate,
          raffleSchema.maxParticipants,
          raffleSchema.totalWinners,
          raffleSchema.institutionId,
          raffleSchema.scheduled,
          raffleSchema.level,
          raffleSchema.status,
          raffleSchema.created_at,
          raffleSchema.updated_at,
        )
        .execute();

      if (result.length === 0) {
        // Try the simple query as a fallback
        return this.getRaffleByIdSimple(id, tx);
      }

      const raffle = result[0];
      if (raffle) {
        raffle.degree = formatField(raffle.degree).flat();
        raffle.programme = formatField(raffle.programme).flat();
        raffle.level = formatField(raffle.level).flat();
        raffle.institutions = formatField(raffle.institutions).flat();
      }

      return raffle;
    } catch (error) {
      this.logger.error(`Error in getRaffleById for ${id}:`, error);
      // Try the simple query as a fallback
      return this.getRaffleByIdSimple(id, tx);
    }
  }

  /**
   * Get a raffle by ID using a simple query (no joins)
   * This is used as a fallback when the complex query fails
   * @param id Raffle ID
   * @param tx Database transaction
   * @returns Basic raffle data
   */
  async getRaffleByIdSimple(id: string, tx = this.drizzle.db): Promise<any> {
    try {
      this.logger.log(`Using simple query to get raffle ${id}`);
      const result = await tx
        .select()
        .from(raffleSchema)
        .where(eq(raffleSchema.id, id))
        .execute();

      if (result.length === 0) {
        throw new Error(`Raffle not found for id: ${id}`);
      }

      const raffle = result[0];
      if (raffle) {
        // Format fields if they exist
        if (raffle.degree) raffle.degree = formatField(raffle.degree);
        if (raffle.programme) raffle.programme = formatField(raffle.programme);
        if (raffle.level) raffle.level = formatField(raffle.level);
      }

      return raffle;
    } catch (error) {
      this.logger.error(`Error in getRaffleByIdSimple for ${id}:`, error);
      throw error;
    }
  }
  async createRaffle(data: any, tx = this.drizzle.db) {
    try {
      const {
        name,
        startDate,
        endDate,
        maxParticipants,
        totalWinners,
        scheduled,
        institutionId,
        degree,
        level,
        programme,
        status,
        description,
        createdBy,
      } = data;

      const formattedDegree = formatField(degree);
      const formattedProgramme = formatField(programme);
      const formattedLevel = formatField(level);

      const raffleResult = await tx
        .insert(raffleSchema)
        .values({
          name: name,
          description: description,
          startDate: startDate,
          endDate: endDate,
          maxParticipants: maxParticipants,
          totalWinners: totalWinners,
          level: formattedLevel,
          degree: formattedDegree,
          scheduled: scheduled,
          programme: formattedProgramme,
          status: scheduled ? status : 'expired',
          createdBy: createdBy,
        })
        .returning();

      const raffleId = raffleResult[0]?.id;

      if (Array.isArray(institutionId) && institutionId.length > 0) {
        const institutionInserts = institutionId.map((id: string) => ({
          raffleId: raffleId,
          institutionId: id,
        }));

        await tx
          .insert(raffleInstitutionsSchema)
          .values(institutionInserts)
          .execute();
      }

      const createdRaffle = raffleResult[0];
      if (createdRaffle) {
        createdRaffle.degree = formatField(createdRaffle.degree);
        createdRaffle.programme = formatField(createdRaffle.programme);
        createdRaffle.level = formatField(createdRaffle.level);
        createdRaffle.institutionId = formatField(institutionId);
      }

      return createdRaffle;
    } catch (error: any) {
      throw error;
    }
  }
  async enterRaffle(data: any, tx = this.drizzle.db) {
    return tx.insert(raffleSchema).values(data).returning();
  }

  /**
   * Batch add participants to a raffle for improved performance
   * @param participantData Array of participant data objects
   * @param tx Database transaction
   * @returns Result of the batch insert
   */
  async batchAddParticipants(participantData: any[], tx = this.drizzle.db) {
    try {
      if (!participantData || participantData.length === 0) {
        return [];
      }

      // Insert participants in batches to avoid overwhelming the database
      const batchSize = 100;
      const results = [];

      for (let i = 0; i < participantData.length; i += batchSize) {
        const batch = participantData.slice(i, i + batchSize);
        const result = await tx
          .insert(participantSchema)
          .values(batch)
          .onConflictDoNothing()
          .returning();
        results.push(...result);
      }

      return results;
    } catch (error: any) {
      this.logger.error('Error in batch add participants:', {
        message: error.message,
        stack: error.stack,
        participantCount: participantData.length,
      });
      throw error;
    }
  }

  async selectWinners(data: any, tx = this.drizzle.db) {
    const result = await tx
      .select()
      .from(participantSchema)
      .where(eq(raffleSchema.id, data.id))
      .execute();
    if (result.length === 0) {
      return { message: 'No participants found', statusCode: 404 };
    }

    const winners = this.shuffle(result).slice(0, data.winners);
    return winners;
  }
  /**
   * Non-modifying shuffle that creates a copy of the array before shuffling
   * @param array Source array to shuffle
   * @returns A new shuffled array
   */
  shuffle = <T>(array: T[]): T[] => {
    // Create a copy of the array to avoid modifying the original
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = randomInt(0, i + 1);
      [newArray[i], newArray[j]] = [newArray[j]!, newArray[i]!];
    }
    return newArray;
  };

  /**
   * Get the number of previous raffle wins for a student
   * @param studentId ID of the student
   * @param excludeRaffleId Optional raffle ID to exclude from the count
   * @param withinDays Optional number of days to look back
   * @returns Number of previous wins
   */
  async getPreviousWins(
    studentId: string,
    excludeRaffleId?: string,
    withinDays?: number,
    tx = this.drizzle.db,
  ): Promise<number> {
    try {
      const filters = [eq(winnersSchema.participantId, studentId)];

      // Exclude specific raffle if provided
      if (excludeRaffleId) {
        filters.push(sql`${winnersSchema.raffleId} != ${excludeRaffleId}`);
      }

      // Add date filter if withinDays is provided
      if (withinDays && withinDays > 0) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - withinDays);
        filters.push(
          sql`${winnersSchema.selectionDate} >= ${cutoffDate.toISOString()}`,
        );
      }

      const result = await tx
        .select({ count: sql<number>`COUNT(*)`.as('count') })
        .from(winnersSchema)
        .where(and(...filters))
        .execute();

      return result[0]?.count || 0;
    } catch (error: any) {
      this.logger.error(
        `Error getting previous wins for student ${studentId}: ${error.message}`,
      );
      return 0; // Return 0 on error to avoid breaking the raffle process
    }
  }

  /**
   * Store a random seed for verification purposes
   * @param raffleId ID of the raffle
   * @param seed Random seed used for selection
   * @returns Result of the operation
   */
  async storeRandomSeed(raffleId: string, seed: string, tx = this.drizzle.db) {
    try {
      // Update the raffle record to store the seed
      const result = await tx
        .update(raffleSchema)
        .set({
          // Store the seed in the description field for now
          // In a production system, you would add a dedicated column for this
          description: sql`CONCAT(${raffleSchema.description}, ' [Verification Seed: ', ${seed}, ']')`,
        })
        .where(eq(raffleSchema.id, raffleId))
        .returning();

      return result;
    } catch (error: any) {
      this.logger.error(
        `Error storing random seed for raffle ${raffleId}: ${error.message}`,
      );
      throw error;
    }
  }
  async getParticipantCount(id: string, tx = this.drizzle.db) {
    const result = await tx
      .select()
      .from(participantSchema)
      .where(eq(raffleSchema.id, id))
      .execute();
    return result.length;
  }
  async addParticipant(data: any, tx = this.drizzle.db) {
    const { studentId, raffleId, entryDate } = data;

    const result = await tx
      .insert(participantSchema)
      .values({
        studentId: studentId,
        raffleId: raffleId,
        entryDate: entryDate,
      })
      .execute();

    return result;
  }
  async addWinner(data: any, tx = this.drizzle.db) {
    const { raffleId, userId } = data;
    return tx
      .insert(winnersSchema)
      .values({
        raffleId: raffleId,
        participantId: userId,
      })
      .returning();
  }
  async updateRaffle(data: any, id: string, tx = this.drizzle.db) {
    try {
      const { startDate, endDate } = data;
      const raffleData = {
        ...data,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
      };

      const result = await tx
        .update(raffleSchema)
        .set(raffleData)
        .where(eq(raffleSchema.id, id))
        .returning();

      if (result.length === 0) {
        throw `Raffle with id ${id} not found`;
      }

      return {
        message: 'Updated successfully',
        statusCode: 200,
      };
    } catch (error: any) {
      throw `Failed to update raffle: ${error.message}`;
    }
  }
  async getParticipantByUserIdAndRaffleId(
    studentId: string,
    raffleId: string,
    tx = this.drizzle.db,
  ) {
    try {
      const participant = await tx
        .select({
          id: participantSchema.id,
          raffleId: participantSchema.raffleId,
          studentId: participantSchema.studentId,
          updated_at: participantSchema.updated_at,
          status:
            sql`CASE WHEN ${winnersSchema.id} IS NOT NULL THEN true ELSE false END`.as(
              'status',
            ),
          studentOther: student_profiles.other_name,
          studentFirstName: student_profiles.first_name,
          studentNameLast: student_profiles.last_name,
          studentProgram: student_profiles.programme,
          enrollmentDate: student_profiles.enrollment_date,
          graduationDate: student_profiles.graduation_date,
          degree: student_profiles.degree,
          institution: institutions.name,
          profilePic: users.profile_pic_url,
        })
        .from(participantSchema)
        .leftJoin(
          winnersSchema,
          and(
            eq(winnersSchema.participantId, participantSchema.studentId),
            eq(winnersSchema.raffleId, participantSchema.raffleId),
          ),
        )
        .leftJoin(
          student_profiles,
          eq(participantSchema.studentId, student_profiles.id),
        )
        .leftJoin(
          institutions,
          eq(student_profiles.institution_id, institutions.id),
        )
        .leftJoin(users, eq(student_profiles.user_id, users.id))
        .where(
          and(
            eq(participantSchema.studentId, studentId),
            eq(participantSchema.raffleId, raffleId),
          ),
        )
        .execute();

      if (!participant.length) {
        return null;
      }

      const studentLevel =
        participant[0] &&
        participant[0].enrollmentDate &&
        participant[0].graduationDate
          ? Math.floor(
              (new Date(participant[0].graduationDate).getTime() -
                new Date(participant[0].enrollmentDate).getTime()) /
                (1000 * 60 * 60 * 24 * 365),
            )
          : null;

      return {
        id: participant[0]?.id,
        raffleId: participant[0]?.raffleId,
        studentId: participant[0]?.studentId,
        updated_at: participant[0]?.updated_at,
        status: Boolean(participant[0]?.status),
        studentOther: participant[0]?.studentOther || null,
        studentFirstName: participant[0]?.studentFirstName || null,
        studentNameLast: participant[0]?.studentNameLast || null,
        studentProgram: participant[0]?.studentProgram || null,
        studentDegrees: participant[0]?.degree || null,
        studentLevel,
        institution: participant[0]?.institution || null,
        profilePic: participant[0]?.profilePic || null,
      };
    } catch (error) {
      throw error;
    }
  }
  async getParticipantsByRaffleId(raffleId: string, tx = this.drizzle.db) {
    const result = await tx
      .select()
      .from(participantSchema)
      .where(eq(participantSchema.raffleId, raffleId))
      .execute();
    return result;
  }

  //Get All Raffles with Params
  async getAllRafflesWithParams(
    params: RaffleFilter,
    tx = this.drizzle.db,
  ): Promise<{ data: RaffleData[]; total: number }> {
    const {
      search,
      institutionId,
      level,
      limit = 10,
      page = 1,
      program,
      raffleId,
      status,
      sort,
      order = 'desc',
    } = params;
    const offset = (page - 1) * limit;

    const filters: any[] = [];

    if (institutionId) {
      filters.push(eq(raffleInstitutionsSchema.institutionId, institutionId));
    }
    if (status) {
      filters.push(eq(raffleSchema.status, status));
    }
    if (program) {
      filters.push(
        sql`${raffleSchema.programme} @> ${sql.raw(`'["${program}"]'::jsonb`)}`,
      );
    }
    if (level) {
      filters.push(
        sql`${raffleSchema.level} @> ${sql.raw(`'[${level}]'::jsonb`)}`,
      );
    }
    if (search) {
      filters.push(ilike(raffleSchema.name, `%${search}%`));
    }
    if (raffleId) {
      filters.push(eq(raffleSchema.id, raffleId));
    }
    // Determine Sort Direction
    let sortDirection;
    if (sort === 'startDate') {
      sortDirection =
        order === 'asc'
          ? asc(raffleSchema.startDate)
          : desc(raffleSchema.startDate);
    } else {
      // Default to sorting by created_at (newest first)
      sortDirection =
        order === 'asc'
          ? asc(raffleSchema.created_at)
          : desc(raffleSchema.created_at);
    }
    try {
      const raffles = await tx
        .select({
          id: raffleSchema.id,
          name: raffleSchema.name,
          description: raffleSchema.description,
          startDate: raffleSchema.startDate,
          endDate: raffleSchema.endDate,
          maxParticipants: raffleSchema.maxParticipants,
          totalWinners: raffleSchema.totalWinners,
          degree: sql`jsonb_agg(DISTINCT ${raffleSchema.degree}::jsonb)`.as(
            'degree',
          ),
          scheduled: raffleSchema.scheduled,
          programme:
            sql`jsonb_agg(DISTINCT ${raffleSchema.programme}::jsonb)`.as(
              'programme',
            ),
          level: raffleSchema.level,
          status: raffleSchema.status,
          created_at: raffleSchema.created_at,
          updated_at: raffleSchema.updated_at,
          institutions: sql`array_agg(DISTINCT ${institutions.name})`.as(
            'institutions',
          ),
          created_by: organisations.name,
        })
        .from(raffleSchema)
        .leftJoin(
          raffleInstitutionsSchema,
          eq(raffleSchema.id, raffleInstitutionsSchema.raffleId),
        )
        .leftJoin(
          institutions,
          eq(raffleInstitutionsSchema.institutionId, institutions.id),
        )
        .leftJoin(
          organisations,
          eq(raffleSchema.createdBy, organisations.user_id),
        )
        .where(and(...filters))
        .groupBy(
          raffleSchema.id,
          raffleSchema.name,
          raffleSchema.description,
          raffleSchema.startDate,
          raffleSchema.endDate,
          raffleSchema.maxParticipants,
          raffleSchema.totalWinners,
          raffleSchema.scheduled,
          raffleSchema.level,
          raffleSchema.status,
          raffleSchema.created_at,
          raffleSchema.updated_at,
          organisations.name,
        )
        .orderBy(sortDirection)
        .limit(limit)
        .offset(offset)
        .execute();

      const [numberOfRaffles]: { raffleCount: number }[] = await tx
        .select({
          raffleCount: sql<number>`COUNT(DISTINCT ${raffleSchema.id})`.as(
            'raffleCount',
          ),
        })
        .from(raffleSchema)
        .leftJoin(
          raffleInstitutionsSchema,
          eq(raffleSchema.id, raffleInstitutionsSchema.raffleId),
        )
        .where(and(...filters))
        .execute();

      const total = numberOfRaffles?.raffleCount || 0;

      return {
        data: raffles.map((raffle: any) => ({
          id: raffle.id,
          name: raffle.name,
          description: raffle.description,
          startDate: raffle.startDate,
          endDate: raffle.endDate,
          maxParticipants: raffle.maxParticipants,
          totalWinners: raffle.totalWinners,
          degree: raffle.degree ? raffle.degree.flat() : [],
          scheduled: raffle.scheduled,
          programme: raffle.programme ? raffle.programme.flat() : [],
          level: raffle.level,
          status: raffle.status,
          created_at: raffle.created_at,
          updated_at: raffle.updated_at,
          created_by: raffle.created_by,
          institutions: Array.isArray(raffle.institutions)
            ? raffle.institutions
            : [],
        })),
        total,
      };
    } catch (error: any) {
      this.logger.error('Error fetching raffles with params:', {
        message: error.message,
        stack: error.stack,
        filters,
      });

      // Return empty result instead of throwing to prevent scheduler crashes
      if (process.env.NODE_ENV !== 'production') {
        throw new Error('Failed to fetch raffles with params');
      } else {
        this.logger.warn(
          'Returning empty result set due to error in production',
        );
        return { data: [], total: 0 };
      }
    }
  }
  async getUniversitiesByIds(institutionIds: string[]): Promise<string[]> {
    const universities = await this.drizzle.db
      .select()
      .from(institutions)
      .where(inArray(institutions.id, institutionIds))
      .execute();

    return universities.map((university) => university.name);
  }
  async selectRaffleWinners(
    data: RaffleFilter,
    tx = this.drizzle.db,
  ): Promise<{ data: any[]; total: number }> {
    const { raffleId, page = 1, limit = 10 } = data;
    const offset = (page - 1) * limit;

    if (!raffleId) {
      this.logger.error('No raffleId provided for selectRaffleWinners');
      throw new Error('Raffle ID is required to fetch winners');
    }

    try {
      // First check if the raffle exists
      const raffleExists = await tx
        .select({ id: raffleSchema.id })
        .from(raffleSchema)
        .where(eq(raffleSchema.id, raffleId))
        .execute();

      if (!raffleExists || raffleExists.length === 0) {
        this.logger.error(`Raffle with ID ${raffleId} not found`);
        throw new NotFoundException(`Raffle with ID ${raffleId} not found`);
      }

      // Check if there are any winners for this raffle
      const winnersCount = await tx
        .select({ count: sql<number>`COUNT(*)`.as('count') })
        .from(winnersSchema)
        .where(eq(winnersSchema.raffleId, raffleId))
        .execute();

      if (winnersCount[0]?.count === 0) {
        this.logger.warn(`No winners found for raffle ${raffleId}`);
        return { data: [], total: 0 };
      }

      const result = await tx
        .select({
          ...getTableColumns(winnersSchema),
          studentProfile: {
            full_name:
              sql`CONCAT(${student_profiles.first_name}, ' ', ${student_profiles.other_name}, ' ', ${student_profiles.last_name})`.as(
                'full_name',
              ),
            program: student_profiles.degree,
            level:
              sql`${student_profiles.graduation_date} - ${student_profiles.enrollment_date}`.as(
                'level',
              ),
          },
          institution: {
            name: institutions.name,
          },
          user: {
            picture: users.profile_pic_url,
          },
        })
        .from(winnersSchema)
        .leftJoin(
          student_profiles,
          eq(winnersSchema.participantId, student_profiles.id),
        )
        .leftJoin(
          institutions,
          eq(student_profiles.institution_id, institutions.id),
        )
        .leftJoin(users, eq(student_profiles.user_id, users.id))
        .where(eq(winnersSchema.raffleId, raffleId))
        .limit(limit)
        .offset(offset)
        .execute();

      const total = winnersCount[0]?.count || 0;

      return {
        data: result,
        total,
      };
    } catch (error: any) {
      this.logger.error(
        `Error fetching raffle winners for ${raffleId}:`,
        error,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to fetch raffle winners: ${error.message}`);
    }
  }
  async getAllRaffleWinners(params: RaffleFilter, tx = this.drizzle.db) {
    const { raffleId, page = 1, limit = 10 } = params;
    const offset = (page - 1) * limit;
    const filters: any[] = [];
    if (raffleId) {
      filters.push(eq(winnersSchema.raffleId, raffleId));
    }

    try {
      const winners = await tx
        .select({
          ...getTableColumns(winnersSchema),
          participant: {
            ...getTableColumns(participantSchema),
          },
        })
        .from(winnersSchema)
        .leftJoin(
          participantSchema,
          eq(winnersSchema.participantId, participantSchema.studentId),
        )
        .where(and(...filters))
        .limit(limit)
        .offset(offset)
        .execute();

      const [totalCount]: { raffleCount: number }[] = await tx
        .select({
          raffleCount: count(),
        })
        .from(winnersSchema)
        .leftJoin(
          participantSchema,
          eq(winnersSchema.participantId, participantSchema.studentId),
        )
        .where(and(...filters))
        .execute();

      const total = totalCount?.raffleCount || 0;

      return {
        data: winners,
        total,
      };
    } catch (error) {
      throw error;
    }
  }

  async activateRaffle(tx = this.drizzle.db): Promise<any[]> {
    try {
      const now = new Date();

      const result = await tx
        .update(raffleSchema)
        .set({ status: 'active' })
        .where(
          and(
            eq(raffleSchema.status, 'draft'),
            lte(raffleSchema.startDate, now.toISOString()),
          ),
        )
        .returning();

      return result;
    } catch (error) {
      throw error;
    }
  }
  async getAllRaffleParticipants(params: RaffleFilter, tx = this.drizzle.db) {
    const {
      institutionId,
      program,
      level,
      sort,
      order = 'desc',
      raffleId,
      search,
      page = 1,
      limit = 10,
      winner,
    } = params;
    const offset = (page - 1) * limit;
    const filters: any[] = [];

    // Raffle ID filter
    if (raffleId) {
      filters.push(eq(participantSchema.raffleId, raffleId));
    }

    // Program filter
    if (program) {
      filters.push(
        eq(
          student_profiles.programme,
          program as 'ICT' | 'Non-STEM' | 'Other STEM',
        ),
      );
    }

    // Institution filter
    if (institutionId) {
      filters.push(eq(institutions.id, institutionId));
    }

    // Search filter
    if (search) {
      filters.push(
        or(
          ilike(student_profiles.first_name, `%${search}%`),
          ilike(student_profiles.last_name, `%${search}%`),
          ilike(student_profiles.other_name, `%${search}%`),
          ilike(institutions.name, `%${search}%`),
        ),
      );
    }

    // Winner filter
    if (winner !== undefined) {
      // The winner value should already be a proper boolean from our shared DTO schema utility
      this.logger.debug(
        `Applying winner filter: ${winner} (type: ${typeof winner})`,
      );
      if (winner === true) {
        filters.push(sql`${winnersSchema.id} IS NOT NULL`);
      } else if (winner === false) {
        filters.push(sql`${winnersSchema.id} IS NULL`);
      }
    }

    // Allowed sort fields
    const allowedSortFields: Record<string, any> = {
      firstName: student_profiles.first_name,
      lastName: student_profiles.last_name,
      enrollmentDate: student_profiles.enrollment_date,
      graduationDate: student_profiles.graduation_date,
      institution: institutions.name,
      program: student_profiles.programme,
    };

    // Default sort field and direction
    const sortField = allowedSortFields[sort!] || participantSchema.id;
    const sortDirection = order === 'asc' ? asc(sortField) : desc(sortField);

    try {
      const participantsQuery = tx
        .select({
          id: participantSchema.id,
          raffleId: participantSchema.raffleId,
          studentId: participantSchema.studentId,
          entryDate: participantSchema.entryDate,
          created_at: participantSchema.created_at,
          updated_at: participantSchema.updated_at,
          status:
            sql`CASE WHEN ${winnersSchema.id} IS NOT NULL THEN true ELSE false END`.as(
              'status',
            ),
          studentOther: student_profiles.other_name,
          studentFirstName: student_profiles.first_name,
          studentNameLast: student_profiles.last_name,
          studentProgram: student_profiles.programme,
          enrollmentDate: student_profiles.enrollment_date,
          graduationDate: student_profiles.graduation_date,
          degree: student_profiles.degree,
          institution: institutions.name,
          institutionId: institutions.id,
          profilePic: users.profile_pic_url,
        })
        .from(participantSchema)
        .leftJoin(
          winnersSchema,
          and(
            eq(winnersSchema.participantId, participantSchema.studentId),
            eq(winnersSchema.raffleId, participantSchema.raffleId),
          ),
        )
        .leftJoin(
          student_profiles,
          eq(participantSchema.studentId, student_profiles.id),
        )
        .leftJoin(
          institutions,
          eq(student_profiles.institution_id, institutions.id),
        )
        .leftJoin(users, eq(student_profiles.user_id, users.id))
        .where(and(...filters))
        .orderBy(sortDirection)
        .limit(limit)
        .offset(offset);

      const participants = await participantsQuery.execute();

      // Calculate level and filter participants
      const filteredParticipants = participants.filter((participant: any) => {
        let studentLevel: number | null = null;
        try {
          const enrollmentYear = participant.enrollmentDate;
          const graduationYear = participant.graduationDate;
          // Ensure years are valid numbers
          if (
            enrollmentYear &&
            graduationYear &&
            !isNaN(enrollmentYear) &&
            !isNaN(graduationYear)
          ) {
            studentLevel = graduationYear - enrollmentYear;
          }
        } catch (error) {
          this.logger.error('Error calculating student level:', error);
          studentLevel = null;
        }
        // Apply level filter if provided
        if (level) {
          if (Array.isArray(level)) {
            return studentLevel !== null && level.includes(studentLevel);
          } else {
            return studentLevel === level;
          }
        }
        return true;
      });

      // Map participants to the desired format
      const mappedParticipants = filteredParticipants.map(
        (participant: any) => {
          const enrollmentYear = participant.enrollmentDate;
          const graduationYear = participant.graduationDate;
          let studentLevel = null;
          if (
            enrollmentYear &&
            graduationYear &&
            !isNaN(enrollmentYear) &&
            !isNaN(graduationYear)
          ) {
            studentLevel = graduationYear - enrollmentYear;
          }
          return {
            id: participant.id,
            raffleId: participant.raffleId,
            studentId: participant.studentId,
            entryDate: participant.entryDate,
            created_at: participant.created_at,
            updated_at: participant.updated_at,
            winner: Boolean(participant.status),
            studentOther: participant.studentOther || null,
            studentFirstName: participant.studentFirstName || null,
            studentNameLast: participant.studentNameLast || null,
            studentProgram: participant.studentProgram || null,
            studentDegrees: participant.degree || null,
            institution: participant.institution || null,
            institutionId: participant.institutionId,
            studentLevel,
            profilePic: participant.profilePic || null,
          };
        },
      );

      const totalCountQuery = tx
        .select({ count: count().as('total') })
        .from(participantSchema)
        .leftJoin(
          winnersSchema,
          and(
            eq(winnersSchema.participantId, participantSchema.studentId),
            eq(winnersSchema.raffleId, participantSchema.raffleId),
          ),
        )
        .leftJoin(
          student_profiles,
          eq(participantSchema.studentId, student_profiles.id),
        )
        .leftJoin(
          institutions,
          eq(student_profiles.institution_id, institutions.id),
        )
        .where(and(...filters));

      const totalCountResult = await totalCountQuery.execute();
      const total = totalCountResult[0]?.count || 0;

      return {
        data: mappedParticipants,
        total,
      };
    } catch (error: any) {
      this.logger.error('Query error:', error.message);
      throw new Error('Failed to get raffle participants');
    }
  }
  async deactivateRaffle(tx = this.drizzle.db): Promise<any[]> {
    try {
      const now = new Date();

      const result = await tx
        .update(raffleSchema)
        .set({ status: 'expired' })
        .where(
          and(
            eq(raffleSchema.status, 'active'),
            lte(raffleSchema.endDate, now.toISOString()),
          ),
        )
        .returning();

      return result;
    } catch (error: any) {
      this.logger.error('Failed to deactivate raffle', error.stack);
      throw new Error('Failed to deactivate raffle');
    }
  }
}
