import { Test } from '@nestjs/testing';
import { BadRequestException, ForbiddenException } from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { JwtHelperService } from '@/jwt-helper/jwt-helper.service';
import { EmailService } from 'src/mail/email.service';
import { AuthService } from './auth.service';
import { AppClients } from '@app/shared/constants/auth.constants';
import { EnvConfig } from '@app/shared/dto/env-config.dto';
// Mock the PointSystemRepository to avoid dependency issues
import { PointSystemRepository } from '@/point-system/repository/point_system.repository';
class MockPointSystemRepository {
  awardPointsToStudent = jest.fn();
  verifyLoginPointsAwarded = jest.fn();
}
import { StudentProfileService } from '@/student_profile/student_profile.service';
import { CacheService } from '@app/shared/redis/cache.service';
import { NotificationService } from '@app/shared/notification/notification.service';
import { QueueService } from '@app/shared/queue/queue.service';
import { UserDeletionService } from '../user-deletion/user-deletion.service';
// Mock the LeaderBoardService to avoid dependency issues
import { LeaderBoardService } from '@/mcq/leader-board/leader-board.service';
class MockLeaderBoardService {
  invalidateUserCache = jest.fn();
  refreshMaterializedViews = jest.fn();
}

import { user_roles, user_states } from '@/db/schema';
import { Request } from 'express';

// Import the VerifyOtpResponse interface
interface VerifyOtpResponse {
  accessToken: string;
  refreshToken: string;
  user: {
    id: string | undefined;
    email: string | undefined;
    state: string | undefined;
    profile_pic_url: string | null;
    student_profile: any | undefined;
    deleted: boolean | undefined;
  };
  cookieOptions: any;
}

describe('AuthService', () => {
  let authService: AuthService;

  const mockDrizzleService = {
    db: {
      query: {
        users: {
          findFirst: jest.fn(),
        },
        token: {
          findFirst: jest.fn(),
        },
      },
      insert: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      select: jest.fn(),
      transaction: jest.fn(),
    },
  };

  const mockJwtHelperService = {
    generateAccessToken: jest.fn(),
    generateAuthTokens: jest.fn(),
    verifyAccessToken: jest.fn(),
    refreshAccessToken: jest.fn(),
    hashOtp: jest.fn(),
    getCookieOptions: jest.fn(),
    generateOtp: jest
      .fn()
      .mockReturnValue({ otpHash: 'hashedOtp', otp: '123456' }),
  };

  const mockEmailService = {
    sendOtp: jest.fn(),
    sendMagicLink: jest.fn(),
    sendEmailSignInLink: jest.fn(),
    waitingListNotification: jest.fn(),
    sendWaitingListAdminNotification: jest.fn(),
    disApproveWaitingListNotification: jest.fn(),
    sendCustomEmail: jest.fn(),
  };

  const mockEnvConfig = {
    MAGIC_LINK_EXPIRY: '15m',
  };

  const mockPointSystemRepository = new MockPointSystemRepository();

  const mockStudentProfileService = {
    getStudentProfileIdByUserId: jest.fn(),
  };

  const mockNotificationService = {
    emitWishListJoined: jest.fn(),
  };

  const mockLeaderBoardService = new MockLeaderBoardService();

  const mockCacheService = {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    invalidateByPrefix: jest.fn(),
    invalidatePattern: jest.fn(),
    generateKey: jest.fn(),
  };

  const mockQueueService = {
    addJob: jest.fn(),
    getJob: jest.fn(),
    removeJob: jest.fn(),
  };

  const mockUserDeletionService = {
    deleteUser: jest.fn(),
    bulkDeleteUsers: jest.fn(),
    deleteUserWithQuestionReassignment: jest.fn().mockResolvedValue({
      success: true,
      deletedUser: {
        id: '123e4567-e89b-12d3-a456-************',
        email: '<EMAIL>',
        role: 'STUDENT',
      },
      constraintStatus: {},
    }),
  };

  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: JwtHelperService,
          useValue: mockJwtHelperService,
        },
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
        {
          provide: EnvConfig,
          useValue: mockEnvConfig,
        },
        {
          provide: PointSystemRepository,
          useValue: mockPointSystemRepository,
        },
        {
          provide: StudentProfileService,
          useValue: mockStudentProfileService,
        },
        {
          provide: NotificationService,
          useValue: mockNotificationService,
        },
        {
          provide: LeaderBoardService,
          useValue: mockLeaderBoardService,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
        {
          provide: QueueService,
          useValue: mockQueueService,
        },
        {
          provide: UserDeletionService,
          useValue: mockUserDeletionService,
        },
        {
          provide: 'UserDeletionService',
          useValue: mockUserDeletionService,
        },
      ],
    }).compile();

    authService = moduleRef.get<AuthService>(AuthService);
  });

  describe('login', () => {
    const mockRequest = {
      headers: {
        'x-client-type': AppClients.MOBILE,
      },
      get: jest.fn((name) => {
        if (name === 'x-client-type') return AppClients.MOBILE;
        return null;
      }),
    } as unknown as Request;

    it('should login mobile user successfully', async () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        role: user_roles.STUDENT,
        deleted: false,
        state: user_states.ACTIVE,
      };

      mockDrizzleService.db.query.users.findFirst.mockResolvedValueOnce(
        mockUser,
      );
      mockDrizzleService.db.insert.mockReturnValue({
        values: jest.fn().mockReturnValue({
          onConflictDoUpdate: jest.fn().mockReturnValue({
            returning: jest.fn().mockResolvedValueOnce({}),
          }),
        }),
      });

      // The login method doesn't return anything for mobile login with active user
      await authService.login(
        { email: '<EMAIL>', redirectUrl: undefined },
        mockRequest,
      );

      // Just verify that sendOtp was called
      expect(mockEmailService.sendOtp).toHaveBeenCalled();
      expect(mockEmailService.sendOtp).toHaveBeenCalledWith(
        '<EMAIL>',
        '123456',
      );
    });

    it('should handle web login successfully', async () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        role: user_roles.ADMIN,
        deleted: false,
      };

      mockDrizzleService.db.query.users.findFirst.mockResolvedValueOnce(
        mockUser,
      );
      mockJwtHelperService.generateAccessToken.mockResolvedValueOnce('token');
      mockDrizzleService.db.insert.mockReturnValue({
        values: jest.fn().mockReturnValue({
          onConflictDoUpdate: jest.fn().mockResolvedValueOnce({}),
        }),
      });

      const result = await authService.webLogin('<EMAIL>');
      expect(result).toBeDefined();
      expect(result).toBe('<EMAIL>');
      expect(mockEmailService.sendEmailSignInLink).toHaveBeenCalled();
    });

    it('should throw ForbiddenException for student trying web login', async () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        role: user_roles.STUDENT,
        deleted: false,
      };

      mockDrizzleService.db.query.users.findFirst.mockResolvedValueOnce(
        mockUser,
      );

      await expect(authService.webLogin('<EMAIL>')).rejects.toThrow(
        ForbiddenException,
      );
    });
  });

  describe('verifyOtp', () => {
    it('should verify OTP successfully', async () => {
      const mockStoredOtp = {
        token: 'hashedOtp',
        expiresAt: new Date(Date.now() + 3600000).toISOString(),
        user_id: '1',
        user: {
          id: '1',
          email: '<EMAIL>',
          state: user_states.ACTIVE,
          profile_pic_url: null,
          student_profile: { id: '1' },
          deleted: false,
        },
      };

      mockJwtHelperService.hashOtp.mockReturnValueOnce('hashedOtp');
      mockDrizzleService.db.query.token.findFirst.mockResolvedValueOnce(
        mockStoredOtp,
      );
      mockJwtHelperService.generateAuthTokens.mockResolvedValueOnce({
        accessToken: 'access',
        refreshToken: 'refresh',
      });
      mockJwtHelperService.getCookieOptions.mockReturnValueOnce({});
      mockStudentProfileService.getStudentProfileIdByUserId.mockResolvedValueOnce(
        '1',
      );
      mockPointSystemRepository.verifyLoginPointsAwarded.mockResolvedValueOnce(
        false,
      );
      mockPointSystemRepository.awardPointsToStudent.mockResolvedValueOnce({});

      const result = (await authService.verifyOtp(
        '123456',
      )) as VerifyOtpResponse;
      expect(result).toBeDefined();
      expect(result.accessToken).toBe('access');
      expect(result.refreshToken).toBe('refresh');
      expect(result.user.id).toBe('1');
      expect(result.user.email).toBe('<EMAIL>');
    });

    it('should throw BadRequestException for expired OTP', async () => {
      mockJwtHelperService.hashOtp.mockReturnValueOnce('hashedOtp');
      mockDrizzleService.db.query.token.findFirst.mockResolvedValueOnce({
        expiresAt: new Date(Date.now() - 3600000).toISOString(),
      });

      await expect(authService.verifyOtp('123456')).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('refreshToken', () => {
    it('should refresh token successfully', async () => {
      const mockRequest = {
        headers: {
          authorization: 'Bearer oldAccessToken',
        },
        get: jest.fn((name) => {
          if (name === 'authorization') return 'Bearer oldAccessToken';
          return null;
        }),
      } as unknown as Request;

      mockJwtHelperService.refreshAccessToken.mockResolvedValueOnce('newToken');

      const result = await authService.refreshAccessToken(
        mockRequest,
        'oldToken',
      );
      expect(result).toBe('newToken');
    });

    it('should throw ForbiddenException for invalid refresh token', async () => {
      const mockRequest = {
        headers: {
          authorization: 'Bearer invalidAccessToken',
        },
        get: jest.fn((name) => {
          if (name === 'authorization') return 'Bearer invalidAccessToken';
          return null;
        }),
      } as unknown as Request;

      mockJwtHelperService.refreshAccessToken.mockResolvedValueOnce(null);

      await expect(
        authService.refreshAccessToken(mockRequest, 'invalidToken'),
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe('deactivateAccount', () => {
    it('should deactivate account successfully', async () => {
      mockDrizzleService.db.update.mockReturnValueOnce({
        set: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            execute: jest.fn().mockResolvedValueOnce({ rowCount: 1 }),
          }),
        }),
      });

      await expect(
        authService.deactivateAccount('userId'),
      ).resolves.not.toThrow();
    });

    it('should throw error when account deactivation fails', async () => {
      mockDrizzleService.db.update.mockReturnValueOnce({
        set: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            execute: jest.fn().mockResolvedValueOnce({ rowCount: 0 }),
          }),
        }),
      });

      await expect(authService.deactivateAccount('userId')).rejects.toThrow(
        'No user found to deactivate',
      );
    });
  });

  describe('deleteOwnAccount', () => {
    const mockUserId = '123e4567-e89b-12d3-a456-************';

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should delete own account successfully with valid confirmation', async () => {
      const result = await authService.deleteOwnAccount(
        mockUserId,
        'delete my account',
      );

      expect(result).toEqual({
        message:
          'Your account and all related data have been permanently deleted',
        deletedUser: {
          id: mockUserId,
          email: '<EMAIL>',
          role: 'STUDENT',
        },
      });
    });

    it('should accept "DELETE" as confirmation text', async () => {
      const result = await authService.deleteOwnAccount(mockUserId, 'DELETE');

      expect(result).toEqual({
        message:
          'Your account and all related data have been permanently deleted',
        deletedUser: {
          id: mockUserId,
          email: '<EMAIL>',
          role: 'STUDENT',
        },
      });
    });

    it('should throw BadRequestException with invalid confirmation text', async () => {
      await expect(
        authService.deleteOwnAccount(mockUserId, 'invalid confirmation'),
      ).rejects.toThrow(
        'Invalid confirmation text. Must type exactly: "DELETE" or "delete my account"',
      );
    });

    it('should throw NotFoundException when user does not exist', async () => {
      mockUserDeletionService.deleteUserWithQuestionReassignment.mockResolvedValueOnce(
        {
          success: false,
          error: 'User account not found',
        },
      );

      await expect(
        authService.deleteOwnAccount(mockUserId, 'delete my account'),
      ).rejects.toThrow('User account not found');
    });
  });

  describe('deleteUser', () => {
    const mockUserId = '123e4567-e89b-12d3-a456-************';
    const adminUserId = 'admin-user-id';

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should delete user successfully', async () => {
      const result = await authService.deleteUser(mockUserId, adminUserId);

      expect(result).toEqual({
        message: 'User and all related data deleted successfully',
        deletedUser: {
          id: mockUserId,
          email: '<EMAIL>',
          role: 'STUDENT',
        },
        cleanupStats: null,
        constraintStatus: {},
      });
    });

    it('should throw NotFoundException when user does not exist', async () => {
      mockUserDeletionService.deleteUserWithQuestionReassignment.mockResolvedValueOnce(
        {
          success: false,
          error: `User with ID ${mockUserId} not found`,
        },
      );

      await expect(
        authService.deleteUser(mockUserId, adminUserId),
      ).rejects.toThrow(`User with ID ${mockUserId} not found`);
    });
  });

  describe('verifyMagicLink', () => {
    it('should verify magic link successfully', async () => {
      const mockToken = 'valid-magic-token';
      const mockStoredToken = {
        token: mockToken,
        user_id: '1',
      };
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        role: user_roles.ADMIN,
        state: user_states.PENDING,
        profile_pic_url: null,
        admin_profile: {
          id: 'org-1',
          name: 'Test Org',
          address: 'Test Address',
          user_id: '1',
        },
        student_profile: null,
      };

      mockDrizzleService.db.query.token.findFirst.mockResolvedValueOnce(
        mockStoredToken,
      );
      mockJwtHelperService.verifyAccessToken.mockResolvedValueOnce({
        userId: '1',
      });
      mockDrizzleService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            leftJoin: jest.fn().mockReturnValue({
              leftJoin: jest.fn().mockResolvedValue([mockUser]),
            }),
          }),
        }),
      });
      mockDrizzleService.db.update.mockReturnValue({
        set: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue({}),
        }),
      });
      mockJwtHelperService.generateAuthTokens.mockResolvedValueOnce({
        accessToken: 'access',
        refreshToken: 'refresh',
      });
      mockJwtHelperService.getCookieOptions.mockReturnValueOnce({});

      const result = await authService.verifyMagicLink(mockToken);

      expect(result).toBeDefined();
      expect(result.accessToken).toBe('access');
      expect(result.user.id).toBe('1');
    });

    it('should throw BadRequestException for invalid magic link', async () => {
      mockDrizzleService.db.query.token.findFirst.mockResolvedValueOnce(null);

      await expect(
        authService.verifyMagicLink('invalid-token'),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw ForbiddenException for invalid token payload', async () => {
      const mockStoredToken = { token: 'token', user_id: '1' };
      mockDrizzleService.db.query.token.findFirst.mockResolvedValueOnce(
        mockStoredToken,
      );
      mockJwtHelperService.verifyAccessToken.mockResolvedValueOnce(null);

      await expect(authService.verifyMagicLink('token')).rejects.toThrow(
        ForbiddenException,
      );
    });
  });

  describe('getWaitingList', () => {
    it('should get waiting list with default parameters', async () => {
      const mockUsers = [
        { id: '1', email: '<EMAIL>', created_at: new Date() },
        { id: '2', email: '<EMAIL>', created_at: new Date() },
      ];
      const mockCount = [{ count: 2 }];

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockResolvedValue(mockUsers),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      // Mock count query
      const mockCountQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue(mockCount),
      };
      mockDrizzleService.db.select.mockReturnValueOnce(mockCountQuery);

      const result = await authService.getWaitingList({
        limit: 10,
        page: 1,
        sort: 'created_at',
        order: 'desc',
        search: '',
        all: false,
      });

      expect(result).toBeDefined();
      expect(result.data).toEqual(mockUsers);
      expect(result.pagination.total).toBe(2);
    });

    it('should handle search filter in waiting list', async () => {
      const filters = {
        search: '<EMAIL>',
        limit: 10,
        page: 1,
        sort: 'created_at',
        order: 'desc' as const,
        all: false,
      };
      const mockUsers = [
        { id: '1', email: '<EMAIL>', created_at: new Date() },
      ];

      // Mock count query first
      const mockSearchCountQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue([{ count: 1 }]),
      };
      mockDrizzleService.db.select.mockReturnValueOnce(mockSearchCountQuery);

      const mockSearchQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockResolvedValue(mockUsers),
      };
      mockDrizzleService.db.select.mockReturnValue(mockSearchQuery);

      const result = await authService.getWaitingList(filters);

      expect(result.data).toEqual(mockUsers);
    });
  });

  describe('disApproveWaitingList', () => {
    it('should disapprove waiting list users successfully', async () => {
      const userIds = ['user-1', 'user-2'];
      const mockUsers = [
        { id: 'user-1', email: '<EMAIL>' },
        { id: 'user-2', email: '<EMAIL>' },
      ];

      mockDrizzleService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue(mockUsers),
        }),
      });

      mockDrizzleService.db.delete.mockReturnValue({
        where: jest.fn().mockReturnValue({
          returning: jest.fn().mockResolvedValue(mockUsers),
        }),
      });

      const result = await authService.disApproveWaitingList(userIds);

      expect(result.total).toBe(2);
      expect(result.data).toHaveLength(2);
      expect(
        mockEmailService.disApproveWaitingListNotification,
      ).toHaveBeenCalledTimes(2);
    });

    it('should handle partial failures in disapproval', async () => {
      const userIds = ['user-1', 'user-2'];
      const mockUsers = [
        { id: 'user-1', email: '<EMAIL>' },
        { id: 'user-2', email: '<EMAIL>' },
      ];

      mockDrizzleService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue(mockUsers),
        }),
      });

      mockDrizzleService.db.delete.mockReturnValue({
        where: jest.fn().mockReturnValue({
          returning: jest.fn().mockResolvedValue([mockUsers[0]]),
        }),
      });

      mockEmailService.disApproveWaitingListNotification = jest
        .fn()
        .mockResolvedValueOnce(undefined)
        .mockRejectedValueOnce(new Error('Email failed'));

      const result = await authService.disApproveWaitingList(userIds);

      expect(result.total).toBe(1);
      expect(result.data).toHaveLength(1);
    });
  });

  describe('checkPendingNonAcademicEmails', () => {
    it('should process pending non-academic emails', async () => {
      const mockNotifyResult = {
        message: 'Sent academic email reminders to 4 users',
        count: 4,
        failures: 1,
        duration: 1000,
      };

      jest
        .spyOn(authService, 'notifyPendingNonAcademicUsers')
        .mockResolvedValueOnce(mockNotifyResult);

      await authService.checkPendingNonAcademicEmails();

      expect(authService.notifyPendingNonAcademicUsers).toHaveBeenCalled();
    });
  });

  describe('notifyPendingNonAcademicUsers', () => {
    it('should notify pending non-academic users successfully', async () => {
      const mockUsers = [
        { id: '1', email: '<EMAIL>', created_at: new Date() },
        { id: '2', email: '<EMAIL>', created_at: new Date() },
      ];

      const mockNotifyQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue(mockUsers),
      };
      mockDrizzleService.db.select.mockReturnValue(mockNotifyQuery);

      mockEmailService.sendCustomEmail = jest.fn().mockResolvedValue(undefined);

      const result = await authService.notifyPendingNonAcademicUsers();

      expect(result.message).toContain('Sent academic email reminders');
      expect(result.count).toBe(2);
      expect(result.failures).toBe(0);
      expect(mockEmailService.sendCustomEmail).toHaveBeenCalledTimes(2);
    });

    it('should handle email sending failures', async () => {
      const mockUsers = [
        { id: '1', email: '<EMAIL>', created_at: new Date() },
        { id: '2', email: '<EMAIL>', created_at: new Date() },
      ];

      const mockFailureQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue(mockUsers),
      };
      mockDrizzleService.db.select.mockReturnValue(mockFailureQuery);

      mockEmailService.sendCustomEmail = jest
        .fn()
        .mockResolvedValueOnce(undefined)
        .mockRejectedValueOnce(new Error('Email failed'));

      const result = await authService.notifyPendingNonAcademicUsers();

      expect(result.message).toContain('Sent academic email reminders');
      expect(result.count).toBe(1);
      expect(result.failures).toBe(1);
      expect(mockEmailService.sendCustomEmail).toHaveBeenCalledTimes(2);
    });

    it('should handle empty user list', async () => {
      const mockEmptyQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue([]),
      };
      mockDrizzleService.db.select.mockReturnValue(mockEmptyQuery);

      const result = await authService.notifyPendingNonAcademicUsers();

      expect(result.message).toBe('No pending users found');
      expect(result.count).toBe(0);
      expect(result.failures).toBe(0);
    });
  });
});
