import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  users,
  posts,
  questionsSchema,
  quizSchema,
  organisations,
  pointsConfigSchema,
  notification_templates,
  scheduled_notifications,
  raffleSchema,
  student_clubs,
} from '@/db/schema';
import { eq, and, or, inArray, sql } from 'drizzle-orm';
import { EmailService } from '@/mail/email.service';
import { z } from 'zod';
import crypto from 'crypto';

const uuidSchema = z.string().uuid('Invalid UUID format');
const emailSchema = z.string().email('Invalid email format');

export interface DeletedUserInfo {
  readonly id: string;
  readonly email: string;
  readonly role: string;
}

export interface CleanupStatistics {
  readonly questionsOrphaned: number;
  readonly questionsReassigned: number;
  readonly quizzesOrphaned: number;
  readonly organisationsOrphaned: number;
  readonly pointsConfigOrphaned: number;
  readonly notificationTemplatesOrphaned: number;
  readonly scheduledNotificationsOrphaned: number;
  readonly rafflesOrphaned: number;
  readonly clubsOrphaned: number;
  readonly studentProfileDeleted: boolean;
  readonly postsDeleted: number;
}

export interface ConstraintStatus {
  readonly migrationApplied: boolean;
  readonly questionsConstraintType: string;
  readonly requiresManualCleanup: boolean;
}

export interface UserDeletionResult {
  readonly success: boolean;
  readonly deletedUser?: DeletedUserInfo;
  readonly cleanupStats?: CleanupStatistics;
  readonly constraintStatus?: ConstraintStatus;
  readonly error?: string;
}

export interface BulkUserDeletionResult {
  readonly totalProcessed: number;
  readonly successCount: number;
  readonly failureCount: number;
  readonly successes: ReadonlyArray<{
    readonly id: string;
    readonly email: string;
    readonly role: string;
    readonly cleanupStats: CleanupStatistics;
  }>;
  readonly failures: ReadonlyArray<{
    readonly id: string;
    readonly email: string;
    readonly error: string;
  }>;
  readonly duration: string;
  readonly startTime: Date;
  readonly endTime: Date;
}

@Injectable()
export class UserDeletionService {
  private readonly logger = new Logger(UserDeletionService.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly emailService: EmailService,
  ) {}

  private validateUUID(value: string, fieldName: string): string {
    try {
      return uuidSchema.parse(value);
    } catch {
      throw new BadRequestException(`${fieldName} must be a valid UUID`);
    }
  }

  private validateEmail(value: string): string {
    try {
      return emailSchema.parse(value.toLowerCase());
    } catch {
      throw new BadRequestException('Invalid email format');
    }
  }

  /**
   * Find and randomly select an admin user for question reassignment
   * @returns A random admin user or null if no admin users are found
   */
  private async findRandomAdminUser(): Promise<{
    id: string;
    email: string;
    role: string;
  } | null> {
    try {
      // Find all active admin users (admin, super_admin, student_admin)
      const adminUsers = await this.drizzle.db
        .select({
          id: users.id,
          email: users.email,
          role: users.role,
        })
        .from(users)
        .where(
          and(
            eq(users.state, 'active'),
            or(
              eq(users.role, 'admin'),
              eq(users.role, 'super_admin'),
              eq(users.role, 'student_admin'),
            ),
          ),
        );

      if (adminUsers.length === 0) {
        this.logger.warn(
          'No active admin users found for question reassignment',
        );
        return null;
      }

      // Randomly select one admin user using crypto.randomInt for security
      const randomIndex = crypto.randomInt(0, adminUsers.length);
      const selectedAdmin = adminUsers[randomIndex];

      if (!selectedAdmin) {
        this.logger.warn('Failed to select admin user from array');
        return null;
      }

      this.logger.log(
        `Selected admin user ${selectedAdmin.email} (${selectedAdmin.role}) for question reassignment`,
      );

      return {
        id: selectedAdmin.id,
        email: selectedAdmin.email,
        role: selectedAdmin.role,
      };
    } catch (error: any) {
      this.logger.error('Failed to find random admin user', error.stack);
      return null;
    }
  }

  private async validateUserExists(userId: string): Promise<void> {
    const userExists = await this.drizzle.db
      .select({ id: users.id })
      .from(users)
      .where(and(eq(users.id, userId), eq(users.deleted, false)))
      .limit(1);

    if (!userExists.length) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }
  }

  async applyConstraintFixes(): Promise<{
    readonly success: boolean;
    readonly message: string;
    readonly appliedFixes: ReadonlyArray<string>;
  }> {
    const appliedFixes: string[] = [];

    try {
      this.logger.log('Applying foreign key constraint fixes');

      await this.drizzle.db.transaction(async (tx) => {
        await tx.execute(sql`
          ALTER TABLE "questions" DROP CONSTRAINT IF EXISTS "questions_created_by_users_id_fk"
        `);
        await tx.execute(sql`
          ALTER TABLE "questions" ADD CONSTRAINT "questions_created_by_users_id_fk"
          FOREIGN KEY ("created_by") REFERENCES "public"."users"("id")
          ON DELETE SET NULL ON UPDATE NO ACTION
        `);
        appliedFixes.push('questions.created_by: RESTRICT → SET NULL');

        await tx.execute(sql`
          ALTER TABLE "quiz" DROP CONSTRAINT IF EXISTS "quiz_created_by_users_id_fk"
        `);
        await tx.execute(sql`
          ALTER TABLE "quiz" ADD CONSTRAINT "quiz_created_by_users_id_fk"
          FOREIGN KEY ("created_by") REFERENCES "public"."users"("id")
          ON DELETE SET NULL ON UPDATE NO ACTION
        `);
        appliedFixes.push(
          'quiz.created_by: Added missing constraint with SET NULL',
        );
      });

      this.logger.log(
        `Applied ${appliedFixes.length} constraint fixes successfully`,
      );

      return {
        success: true,
        message: `Successfully applied ${appliedFixes.length} foreign key constraint fixes`,
        appliedFixes,
      };
    } catch (error: any) {
      this.logger.error('Failed to apply constraint fixes', error.stack);
      throw new InternalServerErrorException(
        `Failed to apply constraint fixes: ${error.message}`,
      );
    }
  }

  async checkConstraintStatus(): Promise<ConstraintStatus> {
    try {
      const result = await this.drizzle.db.execute(sql`
        SELECT
          tc.constraint_name,
          tc.constraint_type,
          rc.delete_rule,
          rc.update_rule
        FROM information_schema.table_constraints tc
        LEFT JOIN information_schema.referential_constraints rc
          ON tc.constraint_name = rc.constraint_name
        WHERE tc.table_name = 'questions'
          AND tc.constraint_name = 'questions_created_by_users_id_fk'
          AND tc.table_schema = 'public'
      `);

      if (result.rows.length === 0) {
        this.logger.warn(
          'Foreign key constraint questions_created_by_users_id_fk not found',
        );
        return {
          migrationApplied: false,
          questionsConstraintType: 'MISSING',
          requiresManualCleanup: true,
        };
      }

      const constraint = result.rows[0] as { delete_rule?: string };
      const deleteRule = constraint.delete_rule?.toUpperCase() || 'UNKNOWN';

      this.logger.debug(`Questions constraint delete rule: ${deleteRule}`);

      return {
        migrationApplied: deleteRule === 'SET NULL',
        questionsConstraintType: deleteRule,
        requiresManualCleanup: deleteRule !== 'SET NULL',
      };
    } catch (error: any) {
      this.logger.error('Failed to check constraint status', error.stack);
      throw new InternalServerErrorException(
        `Failed to check constraint status: ${error.message}`,
      );
    }
  }

  async deleteUserWithQuestionReassignment(
    userIdToDelete: string,
    replacementUserEmail?: string,
    sendNotification: boolean = false,
    adminUserId?: string,
  ): Promise<UserDeletionResult> {
    this.validateUUID(userIdToDelete, 'User ID');
    if (adminUserId) {
      this.validateUUID(adminUserId, 'Admin User ID');
    }
    if (replacementUserEmail) {
      this.validateEmail(replacementUserEmail);
    }

    try {
      const constraintStatus = await this.checkConstraintStatus();

      if (constraintStatus.migrationApplied) {
        this.logger.log('Migration applied, using standard deletion method');
        const result = await this.deleteUser(
          userIdToDelete,
          sendNotification,
          adminUserId,
        );
        return { ...result, constraintStatus };
      }

      this.logger.log('Migration not applied, using manual cleanup approach');
      await this.validateUserExists(userIdToDelete);

      const userToDelete = await this.getUserWithRelations(userIdToDelete);
      const replacementUser = replacementUserEmail
        ? await this.findReplacementUser(replacementUserEmail)
        : null;

      const deletionResult = await this.performManualCleanupDeletion(
        userIdToDelete,
        userToDelete,
        replacementUser,
      );

      if (sendNotification && adminUserId) {
        await this.sendSingleUserDeletionNotification(
          deletionResult,
          adminUserId,
        );
      }

      return {
        success: true,
        deletedUser: deletionResult.deletedUser,
        cleanupStats: deletionResult.cleanupStats,
        constraintStatus,
      };
    } catch (error: any) {
      this.logger.error(`Failed to delete user ${userIdToDelete}`, error.stack);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        `User deletion failed: ${error.message}`,
      );
    }
  }

  private async getUserWithRelations(userId: string) {
    const user = await this.drizzle.db.query.users.findFirst({
      where: eq(users.id, userId),
      with: {
        student_profile: true,
        profile: true,
        posts: true,
      },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    return user;
  }

  private async findReplacementUser(email: string) {
    const normalizedEmail = this.validateEmail(email);
    const replacementUser = await this.drizzle.db.query.users.findFirst({
      where: and(eq(users.email, normalizedEmail), eq(users.deleted, false)),
    });

    if (!replacementUser) {
      throw new NotFoundException(
        `Replacement user with email ${email} not found`,
      );
    }

    this.logger.log(
      `Found replacement user: ${replacementUser.id} (${replacementUser.email})`,
    );
    return replacementUser;
  }

  private async performManualCleanupDeletion(
    userId: string,
    userToDelete: any,
    replacementUser: any,
  ): Promise<{
    deletedUser: DeletedUserInfo;
    cleanupStats: CleanupStatistics;
  }> {
    return await this.drizzle.db.transaction(async (tx) => {
      this.logger.log(
        `Starting manual cleanup deletion for user ${userId} (${userToDelete.email})`,
      );

      const questionsToUpdate = await tx
        .select()
        .from(questionsSchema)
        .where(eq(questionsSchema.created_by, userId));

      let questionsReassigned = 0;
      let questionsOrphaned = 0;

      if (questionsToUpdate.length > 0) {
        // Try to find a replacement user first (either provided or random admin)
        const targetUser =
          replacementUser || (await this.findRandomAdminUser());

        if (targetUser) {
          await tx
            .update(questionsSchema)
            .set({ created_by: targetUser.id })
            .where(eq(questionsSchema.created_by, userId));

          questionsReassigned = questionsToUpdate.length;
          this.logger.log(
            `Reassigned ${questionsReassigned} questions to ${targetUser.email} (${targetUser.role})`,
          );
        } else {
          // Fallback to orphaning if no admin users are available
          await tx
            .update(questionsSchema)
            .set({ created_by: null })
            .where(eq(questionsSchema.created_by, userId));

          questionsOrphaned = questionsToUpdate.length;
          this.logger.warn(
            `No admin users available for reassignment. Orphaned ${questionsOrphaned} questions (set created_by to NULL)`,
          );
        }
      }

      const [
        quizzesCount,
        organisationsCount,
        pointsConfigCount,
        notificationTemplatesCount,
        scheduledNotificationsCount,
        rafflesCount,
        clubsCount,
        postsCount,
      ] = await Promise.all([
        tx.select().from(quizSchema).where(eq(quizSchema.created_by, userId)),
        tx
          .select()
          .from(organisations)
          .where(eq(organisations.user_id, userId)),
        tx
          .select()
          .from(pointsConfigSchema)
          .where(eq(pointsConfigSchema.created_by, userId)),
        tx
          .select()
          .from(notification_templates)
          .where(eq(notification_templates.created_by, userId)),
        tx
          .select()
          .from(scheduled_notifications)
          .where(eq(scheduled_notifications.created_by, userId)),
        tx
          .select()
          .from(raffleSchema)
          .where(eq(raffleSchema.createdBy, userId)),
        tx
          .select()
          .from(student_clubs)
          .where(eq(student_clubs.club_admin, userId)),
        tx.select().from(posts).where(eq(posts.postedBy, userId)),
      ]);

      const deletedUser = await tx
        .delete(users)
        .where(eq(users.id, userId))
        .returning({
          id: users.id,
          email: users.email,
          role: users.role,
        });

      if (!deletedUser.length) {
        throw new InternalServerErrorException('Failed to delete user');
      }

      const user = deletedUser[0];
      if (!user || !user.id || !user.email || !user.role) {
        throw new InternalServerErrorException(
          'Invalid user data returned from deletion',
        );
      }

      this.logger.log(
        `Successfully deleted user ${userId} with manual cleanup`,
      );

      return {
        deletedUser: {
          id: user.id,
          email: user.email,
          role: user.role,
        },
        cleanupStats: {
          questionsOrphaned,
          questionsReassigned,
          quizzesOrphaned: quizzesCount.length,
          organisationsOrphaned: organisationsCount.length,
          pointsConfigOrphaned: pointsConfigCount.length,
          notificationTemplatesOrphaned: notificationTemplatesCount.length,
          scheduledNotificationsOrphaned: scheduledNotificationsCount.length,
          rafflesOrphaned: rafflesCount.length,
          clubsOrphaned: clubsCount.length,
          studentProfileDeleted: !!userToDelete.student_profile,
          postsDeleted: postsCount.length,
        },
      };
    });
  }

  /**
   * Delete a user with retry mechanism for timeout errors
   * @param userId The user ID to delete
   * @param sendNotification Whether to send notification
   * @param adminUserId The admin user ID for notifications
   * @returns The deletion result
   */
  private async deleteUserWithRetry(
    userId: string,
    sendNotification: boolean = false,
    adminUserId?: string,
  ): Promise<UserDeletionResult> {
    try {
      return await this.deleteUser(userId, sendNotification, adminUserId);
    } catch (error: any) {
      // Check if this is a query timeout error that should be retried
      if (this.isQueryTimeoutError(error)) {
        this.logger.warn(
          `Query timeout detected for user ${userId}, attempting retry...`,
        );

        try {
          // Wait a brief moment before retry to allow database recovery
          await new Promise((resolve) => setTimeout(resolve, 1000));
          return await this.deleteUser(userId, sendNotification, adminUserId);
        } catch (retryError: any) {
          this.logger.error(
            `Retry failed for user ${userId}: ${retryError.message}`,
          );
          // Re-throw the original timeout error for consistent error reporting
          throw error;
        }
      }

      // For non-timeout errors, throw immediately without retry
      throw error;
    }
  }

  /**
   * Check if an error is a query timeout that should be retried
   * @param error The error to check
   * @returns True if the error is a query timeout
   */
  private isQueryTimeoutError(error: any): boolean {
    const errorMessage = error.message?.toLowerCase() || '';
    return (
      errorMessage.includes('query read timeout') ||
      (errorMessage.includes('timeout') && errorMessage.includes('query'))
    );
  }

  async deleteUser(
    userId: string,
    sendNotification: boolean = false,
    adminUserId?: string,
  ): Promise<UserDeletionResult> {
    this.validateUUID(userId, 'User ID');
    if (adminUserId) {
      this.validateUUID(adminUserId, 'Admin User ID');
    }

    try {
      await this.validateUserExists(userId);
      const userToDelete = await this.getUserWithRelations(userId);
      const deletionResult = await this.performStandardDeletion(
        userId,
        userToDelete,
      );

      if (sendNotification && adminUserId) {
        await this.sendSingleUserDeletionNotification(
          deletionResult,
          adminUserId,
        );
      }

      return {
        success: true,
        deletedUser: deletionResult.deletedUser,
        cleanupStats: deletionResult.cleanupStats,
      };
    } catch (error: any) {
      this.logger.error(`Failed to delete user ${userId}`, error.stack);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        `User deletion failed: ${error.message}`,
      );
    }
  }

  private async performStandardDeletion(
    userId: string,
    userToDelete: any,
  ): Promise<{
    deletedUser: DeletedUserInfo;
    cleanupStats: CleanupStatistics;
  }> {
    return await this.drizzle.db.transaction(async (tx) => {
      this.logger.log(
        `Starting comprehensive deletion for user ${userId} (${userToDelete.email})`,
      );

      // Handle question reassignment before user deletion
      const questionsToReassign = await tx
        .select()
        .from(questionsSchema)
        .where(eq(questionsSchema.created_by, userId));

      let questionsReassigned = 0;
      let questionsOrphaned = 0;

      if (questionsToReassign.length > 0) {
        const adminUser = await this.findRandomAdminUser();

        if (adminUser) {
          await tx
            .update(questionsSchema)
            .set({ created_by: adminUser.id })
            .where(eq(questionsSchema.created_by, userId));

          questionsReassigned = questionsToReassign.length;
          this.logger.log(
            `Reassigned ${questionsReassigned} questions to ${adminUser.email} (${adminUser.role})`,
          );
        } else {
          // Questions will be orphaned by the SET NULL constraint
          questionsOrphaned = questionsToReassign.length;
          this.logger.warn(
            `No admin users available for reassignment. ${questionsOrphaned} questions will be orphaned`,
          );
        }
      }

      const [
        quizzesCount,
        organisationsCount,
        pointsConfigCount,
        notificationTemplatesCount,
        scheduledNotificationsCount,
        rafflesCount,
        clubsCount,
        postsCount,
      ] = await Promise.all([
        tx.select().from(quizSchema).where(eq(quizSchema.created_by, userId)),
        tx
          .select()
          .from(organisations)
          .where(eq(organisations.user_id, userId)),
        tx
          .select()
          .from(pointsConfigSchema)
          .where(eq(pointsConfigSchema.created_by, userId)),
        tx
          .select()
          .from(notification_templates)
          .where(eq(notification_templates.created_by, userId)),
        tx
          .select()
          .from(scheduled_notifications)
          .where(eq(scheduled_notifications.created_by, userId)),
        tx
          .select()
          .from(raffleSchema)
          .where(eq(raffleSchema.createdBy, userId)),
        tx
          .select()
          .from(student_clubs)
          .where(eq(student_clubs.club_admin, userId)),
        tx.select().from(posts).where(eq(posts.postedBy, userId)),
      ]);

      const deletedUser = await tx
        .delete(users)
        .where(eq(users.id, userId))
        .returning({
          id: users.id,
          email: users.email,
          role: users.role,
        });

      if (!deletedUser.length) {
        throw new InternalServerErrorException('Failed to delete user');
      }

      const user = deletedUser[0];
      if (!user || !user.id || !user.email || !user.role) {
        throw new InternalServerErrorException(
          'Invalid user data returned from deletion',
        );
      }

      this.logger.log(
        `Successfully deleted user ${userId} with comprehensive cleanup`,
      );

      return {
        deletedUser: {
          id: user.id,
          email: user.email,
          role: user.role,
        },
        cleanupStats: {
          questionsOrphaned,
          questionsReassigned,
          quizzesOrphaned: quizzesCount.length,
          organisationsOrphaned: organisationsCount.length,
          pointsConfigOrphaned: pointsConfigCount.length,
          notificationTemplatesOrphaned: notificationTemplatesCount.length,
          scheduledNotificationsOrphaned: scheduledNotificationsCount.length,
          rafflesOrphaned: rafflesCount.length,
          clubsOrphaned: clubsCount.length,
          studentProfileDeleted: !!userToDelete.student_profile,
          postsDeleted: postsCount.length,
        },
      };
    });
  }

  async bulkDeleteUsers(
    userIds: string[],
    adminUserId: string,
  ): Promise<BulkUserDeletionResult> {
    this.validateUUID(adminUserId, 'Admin User ID');
    userIds.forEach((id, index) =>
      this.validateUUID(id, `User ID at index ${index}`),
    );

    if (userIds.length === 0) {
      throw new BadRequestException('At least one user ID is required');
    }

    if (userIds.length > 100) {
      throw new BadRequestException(
        'Cannot delete more than 100 users at once',
      );
    }

    const startTime = new Date();
    const successes: Array<{
      id: string;
      email: string;
      role: string;
      cleanupStats: CleanupStatistics;
    }> = [];
    const failures: Array<{
      id: string;
      email: string;
      error: string;
    }> = [];

    this.logger.log(
      `Starting bulk deletion of ${userIds.length} users by admin ${adminUserId}`,
    );

    try {
      const existingUsers = await this.drizzle.db
        .select({ id: users.id, email: users.email, role: users.role })
        .from(users)
        .where(and(inArray(users.id, userIds), eq(users.deleted, false)));

      if (existingUsers.length === 0) {
        const endTime = new Date();
        return {
          totalProcessed: 0,
          successCount: 0,
          failureCount: 0,
          successes: [],
          failures: [],
          duration: this.calculateDuration(startTime, endTime),
          startTime,
          endTime,
        };
      }

      for (const user of existingUsers) {
        try {
          const result = await this.deleteUserWithRetry(user.id);

          if (result.success && result.deletedUser && result.cleanupStats) {
            successes.push({
              id: result.deletedUser.id,
              email: result.deletedUser.email,
              role: result.deletedUser.role,
              cleanupStats: result.cleanupStats,
            });
          } else {
            failures.push({
              id: user.id,
              email: user.email,
              error: result.error || 'Unknown error occurred',
            });
          }
        } catch (error: any) {
          failures.push({
            id: user.id,
            email: user.email,
            error: error.message,
          });
        }
      }

      const endTime = new Date();
      const bulkResult: BulkUserDeletionResult = {
        totalProcessed: existingUsers.length,
        successCount: successes.length,
        failureCount: failures.length,
        successes,
        failures,
        duration: this.calculateDuration(startTime, endTime),
        startTime,
        endTime,
      };

      await this.sendBulkDeletionNotification(bulkResult, adminUserId);

      this.logger.log(
        `Bulk deletion completed: ${successes.length} successful, ${failures.length} failed out of ${existingUsers.length} total`,
      );

      return bulkResult;
    } catch (error: any) {
      this.logger.error('Bulk deletion failed', error.stack);
      throw new InternalServerErrorException(
        `Bulk deletion failed: ${error.message}`,
      );
    }
  }

  private async sendSingleUserDeletionNotification(
    result: { deletedUser: DeletedUserInfo; cleanupStats: CleanupStatistics },
    adminUserId: string,
  ): Promise<void> {
    try {
      const [adminUser, adminUsers] = await Promise.all([
        this.drizzle.db.query.users.findFirst({
          where: and(eq(users.id, adminUserId), eq(users.deleted, false)),
          columns: { email: true },
        }),
        this.drizzle.db
          .select({ email: users.email })
          .from(users)
          .where(
            and(
              eq(users.role, 'super_admin'),
              eq(users.state, 'active'),
              eq(users.deleted, false),
            ),
          ),
      ]);

      if (!adminUser) {
        this.logger.warn(
          `Admin user ${adminUserId} not found for email notification`,
        );
        return;
      }

      if (adminUsers.length === 0) {
        this.logger.warn(
          'No active admin users found for user deletion notification',
        );
        return;
      }

      const emailContext = {
        adminName: this.formatAdminName(adminUser.email),
        deletedUser: result.deletedUser,
        cleanupStats: result.cleanupStats,
        completedAt: new Date().toLocaleString(),
      };

      await Promise.all(
        adminUsers.map((admin) =>
          this.emailService.sendCustomEmail({
            email: admin.email,
            subject: '🗑️ User Deletion Report - Operation Complete',
            template: 'user-deletion-report',
            context: emailContext,
          }),
        ),
      );

      this.logger.log(
        `Sent user deletion notification to ${adminUsers.length} admin users`,
      );
    } catch (error: any) {
      this.logger.error(
        'Failed to send user deletion notification',
        error.stack,
      );
    }
  }

  private async sendBulkDeletionNotification(
    result: BulkUserDeletionResult,
    adminUserId: string,
  ): Promise<void> {
    try {
      const [adminUser, adminUsers] = await Promise.all([
        this.drizzle.db.query.users.findFirst({
          where: and(eq(users.id, adminUserId), eq(users.deleted, false)),
          columns: { email: true },
        }),
        this.drizzle.db
          .select({ email: users.email })
          .from(users)
          .where(
            and(
              eq(users.role, 'super_admin'),
              eq(users.state, 'active'),
              eq(users.deleted, false),
            ),
          ),
      ]);

      if (!adminUser) {
        this.logger.warn(
          `Admin user ${adminUserId} not found for email notification`,
        );
        return;
      }

      if (adminUsers.length === 0) {
        this.logger.warn(
          'No active admin users found for bulk deletion notification',
        );
        return;
      }

      const emailContext = {
        adminName: this.formatAdminName(adminUser.email),
        totalProcessed: result.totalProcessed,
        successCount: result.successCount,
        failureCount: result.failureCount,
        duration: result.duration,
        completedAt: result.endTime.toLocaleString(),
        failures: result.failures,
      };

      await Promise.all(
        adminUsers.map((admin) =>
          this.emailService.sendCustomEmail({
            email: admin.email,
            subject: '🗑️ Bulk User Deletion Report - Operation Complete',
            template: 'bulk-deletion-report',
            context: emailContext,
          }),
        ),
      );

      this.logger.log(
        `Sent bulk deletion notification to ${adminUsers.length} admin users`,
      );
    } catch (error: any) {
      this.logger.error(
        'Failed to send bulk deletion notification',
        error.stack,
      );
    }
  }

  private calculateDuration(startTime: Date, endTime: Date): string {
    const durationMs = endTime.getTime() - startTime.getTime();
    const seconds = Math.floor(durationMs / 1000);
    const minutes = Math.floor(seconds / 60);

    if (minutes > 0) {
      const remainingSeconds = seconds % 60;
      return `${minutes}m ${remainingSeconds}s`;
    }

    return `${seconds}s`;
  }

  private formatAdminName(email: string): string {
    const username = email.split('@')[0];
    if (!username) {
      return 'Admin';
    }

    // Convert email prefix to a more readable format
    // e.g., "rexford.koomson" -> "Rexford Koomson"
    return (
      username
        .split(/[._-]/)
        .filter((part) => part.length > 0)
        .map(
          (part) => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase(),
        )
        .join(' ') || 'Admin'
    );
  }
}
