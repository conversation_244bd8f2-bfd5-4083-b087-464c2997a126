import { Test, TestingModule } from '@nestjs/testing';
import { UserDeletionService } from './user-deletion.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { EmailService } from '@/mail/email.service';
import { QueueService } from '@app/shared/queue/queue.service';

describe('UserDeletionService Integration', () => {
  let service: UserDeletionService;
  let mockDrizzleService: any;
  let testUserId: string;

  beforeAll(async () => {
    const mockQueryBase = {
      findFirst: jest.fn(),
      findMany: jest.fn(),
    };

    // Create separate mock objects for each table
    const mockUsersQuery = { ...mockQueryBase };
    const mockStudentProfilesQuery = { ...mockQueryBase };
    const mockQuestionsQuery = { ...mockQueryBase };
    const mockQuizQuery = { ...mockQueryBase };

    const mockInsert = jest.fn().mockReturnValue({
      values: jest.fn().mockReturnValue({
        returning: jest.fn().mockResolvedValue([{ id: 'test-id' }]),
      }),
    });

    const mockDelete = jest.fn().mockReturnValue({
      where: jest.fn().mockReturnValue({
        returning: jest.fn().mockResolvedValue([]),
      }),
    });

    const mockUpdate = jest.fn().mockReturnValue({
      set: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnValue({
          execute: jest.fn().mockResolvedValue([]),
        }),
      }),
    });

    const mockSelect = jest.fn().mockReturnValue({
      from: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnValue({
          limit: jest.fn().mockResolvedValue([]), // Added limit method for query chaining
        }),
      }),
    });

    const mockTransaction = jest.fn().mockImplementation(async (callback) => {
      const mockTx = {
        insert: mockInsert,
        delete: mockDelete,
        update: mockUpdate,
        select: mockSelect,
        query: mockQueryBase,
      };
      return await callback(mockTx);
    });

    mockDrizzleService = {
      db: {
        insert: mockInsert,
        delete: mockDelete,
        update: mockUpdate,
        select: mockSelect,
        query: {
          ...mockQueryBase,
          questionsSchema: mockQuestionsQuery,
          quizSchema: mockQuizQuery,
          student_profiles: mockStudentProfilesQuery,
          users: mockUsersQuery,
        },
        transaction: mockTransaction,
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserDeletionService,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: EmailService,
          useValue: {
            sendCustomEmail: jest.fn(),
          },
        },
        {
          provide: QueueService,
          useValue: {
            addSingleEmailJob: jest.fn(),
            addBulkEmailJob: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UserDeletionService>(UserDeletionService);
  });

  beforeEach(() => {
    testUserId = '123e4567-e89b-12d3-a456-************'; // Valid UUID format

    // Setup mock responses for the user deletion service
    const mockUser = {
      id: testUserId,
      email: '<EMAIL>',
      role: 'student',
      state: 'active',
      deleted: false,
    };

    // Mock the user validation (select query with limit)
    mockDrizzleService.db.select = jest.fn().mockReturnValue({
      from: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnValue({
          limit: jest.fn().mockResolvedValue([mockUser]), // Return user for validation
        }),
      }),
    });

    // Mock the user lookup
    mockDrizzleService.db.query.users.findFirst = jest
      .fn()
      .mockResolvedValue(mockUser);

    // Mock the transaction implementation
    mockDrizzleService.db.transaction = jest
      .fn()
      .mockImplementation(async (callback) => {
        const mockTx = {
          delete: jest.fn().mockReturnValue({
            where: jest.fn().mockReturnValue({
              returning: jest.fn().mockResolvedValue([mockUser]),
            }),
          }),
          update: jest.fn().mockReturnValue({
            set: jest.fn().mockReturnValue({
              where: jest.fn().mockReturnValue({
                execute: jest.fn().mockResolvedValue([]),
              }),
            }),
          }),
          select: jest.fn().mockReturnValue({
            from: jest.fn().mockReturnValue({
              where: jest.fn().mockResolvedValue([]),
            }),
          }),
        };
        return await callback(mockTx);
      });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Foreign Key Constraint Handling', () => {
    it('should handle questions with SET NULL constraint', async () => {
      // Setup mock data
      const mockQuestion = {
        id: 'question-id',
        question: 'Test question',
        created_by: testUserId,
      };

      // Mock the question query to return the question before deletion
      mockDrizzleService.db.query.questionsSchema.findFirst = jest
        .fn()
        .mockResolvedValueOnce(mockQuestion) // First call: question exists with created_by
        .mockResolvedValueOnce({ ...mockQuestion, created_by: null }); // Second call: question exists with created_by = null

      // Mock transaction to simulate the SET NULL behavior
      mockDrizzleService.db.transaction = jest
        .fn()
        .mockImplementation(async (callback) => {
          const mockTx = {
            delete: jest.fn().mockReturnValue({
              where: jest.fn().mockReturnValue({
                returning: jest.fn().mockResolvedValue([
                  {
                    id: testUserId,
                    email: '<EMAIL>',
                    role: 'student',
                  },
                ]),
              }),
            }),
            update: jest.fn().mockReturnValue({
              set: jest.fn().mockReturnValue({
                where: jest.fn().mockReturnValue({
                  execute: jest.fn().mockResolvedValue([]),
                }),
              }),
            }),
            select: jest.fn().mockReturnValue({
              from: jest.fn().mockReturnValue({
                where: jest.fn().mockResolvedValue([mockQuestion]), // Return orphaned questions
              }),
            }),
          };
          return await callback(mockTx);
        });

      // Delete the user
      const result = await service.deleteUser(testUserId);

      expect(result.success).toBe(true);
      expect(result.cleanupStats?.questionsOrphaned).toBe(1);

      // Reset the question mock to return question with created_by set to null
      mockDrizzleService.db.query.questionsSchema.findFirst = jest
        .fn()
        .mockResolvedValue({ ...mockQuestion, created_by: null });

      // Verify the question still exists but with created_by set to NULL
      const remainingQuestion =
        await mockDrizzleService.db.query.questionsSchema.findFirst();
      expect(remainingQuestion).toBeDefined();
      expect(remainingQuestion?.created_by).toBeNull();
    });

    it('should handle quiz with SET NULL constraint', async () => {
      // Setup mock data
      const mockQuiz = {
        id: 'quiz-id',
        title: 'Test Quiz',
        created_by: testUserId,
      };

      // Mock the quiz query
      mockDrizzleService.db.query.quizSchema.findFirst = jest
        .fn()
        .mockResolvedValueOnce(mockQuiz) // First call: quiz exists with created_by
        .mockResolvedValueOnce({ ...mockQuiz, created_by: null }); // Second call: quiz exists with created_by = null

      // Mock transaction to simulate the SET NULL behavior
      mockDrizzleService.db.transaction = jest
        .fn()
        .mockImplementation(async (callback) => {
          const mockTx = {
            delete: jest.fn().mockReturnValue({
              where: jest.fn().mockReturnValue({
                returning: jest.fn().mockResolvedValue([
                  {
                    id: testUserId,
                    email: '<EMAIL>',
                    role: 'student',
                  },
                ]),
              }),
            }),
            update: jest.fn().mockReturnValue({
              set: jest.fn().mockReturnValue({
                where: jest.fn().mockReturnValue({
                  execute: jest.fn().mockResolvedValue([]),
                }),
              }),
            }),
            select: jest.fn().mockReturnValue({
              from: jest.fn().mockReturnValue({
                where: jest.fn().mockResolvedValue([mockQuiz]), // Return orphaned quizzes
              }),
            }),
          };
          return await callback(mockTx);
        });

      // Delete the user
      const result = await service.deleteUser(testUserId);

      expect(result.success).toBe(true);
      expect(result.cleanupStats?.quizzesOrphaned).toBe(1);

      // Reset the quiz mock to return quiz with created_by set to null
      mockDrizzleService.db.query.quizSchema.findFirst = jest
        .fn()
        .mockResolvedValue({ ...mockQuiz, created_by: null });

      // Verify the quiz still exists but with created_by set to NULL
      const remainingQuiz =
        await mockDrizzleService.db.query.quizSchema.findFirst();
      expect(remainingQuiz).toBeDefined();
      expect(remainingQuiz?.created_by).toBeNull();
    });

    it('should cascade delete student profile', async () => {
      // Setup mock data
      const mockStudentProfile = {
        id: 'profile-id',
        user_id: testUserId,
        first_name: 'Test',
        last_name: 'User',
      };

      // Reset and setup specific mocks for this test
      jest.clearAllMocks();

      // Mock the student profile query to return undefined after deletion
      mockDrizzleService.db.query.student_profiles.findFirst = jest
        .fn()
        .mockResolvedValue(undefined); // Profile deleted after user deletion

      // Mock transaction to simulate CASCADE DELETE behavior
      mockDrizzleService.db.transaction = jest
        .fn()
        .mockImplementation(async (callback) => {
          const mockTx = {
            delete: jest.fn().mockReturnValue({
              where: jest.fn().mockReturnValue({
                returning: jest.fn().mockResolvedValue([
                  {
                    id: testUserId,
                    email: '<EMAIL>',
                    role: 'student',
                  },
                ]),
              }),
            }),
            update: jest.fn().mockReturnValue({
              set: jest.fn().mockReturnValue({
                where: jest.fn().mockReturnValue({
                  execute: jest.fn().mockResolvedValue([]),
                }),
              }),
            }),
            select: jest.fn().mockReturnValue({
              from: jest.fn().mockReturnValue({
                where: jest.fn().mockResolvedValue([]), // No orphaned data
              }),
            }),
          };
          return await callback(mockTx);
        });

      // Mock the validateUserExists query to find user with student profile
      mockDrizzleService.db.select = jest.fn().mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([
              {
                id: testUserId,
                email: '<EMAIL>',
                role: 'student',
                state: 'active',
                deleted: false,
              },
            ]),
          }),
        }),
      });

      // Mock getUserWithRelations method to return user with student profile
      mockDrizzleService.db.query.users.findFirst = jest
        .fn()
        .mockResolvedValue({
          id: testUserId,
          email: '<EMAIL>',
          role: 'student',
          state: 'active',
          deleted: false,
          student_profile: mockStudentProfile, // Include student profile
          profile: null,
          posts: [],
        });

      // Delete the user
      const result = await service.deleteUser(testUserId);

      expect(result.success).toBe(true);
      expect(result.cleanupStats?.studentProfileDeleted).toBe(true);

      // Reset the student profile mock to return undefined after deletion
      mockDrizzleService.db.query.student_profiles.findFirst = jest
        .fn()
        .mockResolvedValue(undefined);

      // Verify student profile is deleted
      const profileAfter =
        await mockDrizzleService.db.query.student_profiles.findFirst();
      expect(profileAfter).toBeUndefined();
    });

    it('should handle user deletion without foreign key violations', async () => {
      // Mock transaction for successful user deletion
      mockDrizzleService.db.transaction = jest
        .fn()
        .mockImplementation(async (callback) => {
          const mockTx = {
            delete: jest.fn().mockReturnValue({
              where: jest.fn().mockReturnValue({
                returning: jest.fn().mockResolvedValue([
                  {
                    id: testUserId,
                    email: '<EMAIL>',
                    role: 'student',
                  },
                ]),
              }),
            }),
            update: jest.fn().mockReturnValue({
              set: jest.fn().mockReturnValue({
                where: jest.fn().mockReturnValue({
                  execute: jest.fn().mockResolvedValue([]),
                }),
              }),
            }),
            select: jest.fn().mockReturnValue({
              from: jest.fn().mockReturnValue({
                where: jest.fn().mockResolvedValue([]), // No orphaned data
              }),
            }),
          };
          return await callback(mockTx);
        });

      // Mock user query to return undefined after deletion
      mockDrizzleService.db.query.users.findFirst = jest
        .fn()
        .mockResolvedValueOnce({
          id: testUserId,
          email: '<EMAIL>',
          role: 'student',
        }) // First call: user exists
        .mockResolvedValueOnce(undefined); // Second call: user deleted

      const result = await service.deleteUser(testUserId);

      expect(result.success).toBe(true);
      expect(result.deletedUser).toBeDefined();
      expect(result.deletedUser?.id).toBe(testUserId);

      // Verify user is actually deleted
      const deletedUser = await mockDrizzleService.db.query.users.findFirst();
      expect(deletedUser).toBeUndefined();
    });
  });

  describe('Cleanup Statistics', () => {
    it('should provide accurate cleanup statistics', async () => {
      // Setup mock transaction with cleanup stats
      mockDrizzleService.db.transaction = jest
        .fn()
        .mockImplementation(async (callback) => {
          let selectCallCount = 0;

          const mockTx = {
            delete: jest.fn().mockReturnValue({
              where: jest.fn().mockReturnValue({
                returning: jest.fn().mockResolvedValue([
                  {
                    id: testUserId,
                    email: '<EMAIL>',
                    role: 'student',
                  },
                ]),
              }),
            }),
            update: jest.fn().mockReturnValue({
              set: jest.fn().mockReturnValue({
                where: jest.fn().mockReturnValue({
                  execute: jest.fn().mockResolvedValue([]),
                }),
              }),
            }),
            select: jest.fn().mockImplementation(() => ({
              from: jest.fn().mockReturnValue({
                where: jest.fn().mockImplementation(() => {
                  selectCallCount++;

                  if (selectCallCount === 1) {
                    // First call: questions
                    return Promise.resolve([{ id: 'q1' }, { id: 'q2' }]);
                  } else if (selectCallCount === 2) {
                    // Second call: quizzes
                    return Promise.resolve([{ id: 'quiz1' }]);
                  } else {
                    // All other calls: empty arrays
                    return Promise.resolve([]);
                  }
                }),
              }),
            })),
          };
          return await callback(mockTx);
        });

      // Mock the validateUserExists query to find user with student profile
      mockDrizzleService.db.select = jest.fn().mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([
              {
                id: testUserId,
                email: '<EMAIL>',
                role: 'student',
                state: 'active',
                deleted: false,
              },
            ]),
          }),
        }),
      });

      // Mock getUserWithRelations method to return user with student profile
      mockDrizzleService.db.query.users.findFirst = jest
        .fn()
        .mockResolvedValue({
          id: testUserId,
          email: '<EMAIL>',
          role: 'student',
          state: 'active',
          deleted: false,
          student_profile: { id: 'profile-id' }, // Include student profile
          profile: null,
          posts: [],
        });

      const result = await service.deleteUser(testUserId);

      expect(result.success).toBe(true);
      expect(result.cleanupStats).toBeDefined();
      expect(result.cleanupStats?.questionsOrphaned).toBe(2);
      expect(result.cleanupStats?.quizzesOrphaned).toBe(1);
      expect(result.cleanupStats?.studentProfileDeleted).toBe(true);
    });
  });
});

// Note: This integration test requires:
// 1. A test database setup
// 2. Valid foreign key references (country_id, institution_id, question_bank_id)
// 3. The migration 0018_fix-remaining-user-deletion-constraints.sql to be applied
//
// In a real test environment, you would:
// 1. Set up test data fixtures with valid foreign key references
// 2. Use a test database that mirrors the production schema
// 3. Run migrations before tests
// 4. Clean up test data after each test
