import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { ApiProperty } from '@nestjs/swagger';

const deleteUserWithReassignmentSchema = z.object({
  replacementUserEmail: z
    .string()
    .email('Invalid email format')
    .optional()
    .transform((val) => val?.toLowerCase()),
  sendNotification: z.boolean().default(false),
});

const bulkDeleteUsersSchema = z.object({
  userIds: z
    .array(z.string().uuid('Invalid user ID format'))
    .min(1, 'At least one user ID is required')
    .max(100, 'Cannot delete more than 100 users at once'),
});

const constraintFixesResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  appliedFixes: z.array(z.string()),
});

const constraintStatusSchema = z.object({
  migrationApplied: z.boolean(),
  questionsConstraintType: z.string(),
  requiresManualCleanup: z.boolean(),
});

const cleanupStatsSchema = z.object({
  questionsOrphaned: z.number().int().min(0),
  questionsReassigned: z.number().int().min(0),
  quizzesOrphaned: z.number().int().min(0),
  organisationsOrphaned: z.number().int().min(0),
  pointsConfigOrphaned: z.number().int().min(0),
  notificationTemplatesOrphaned: z.number().int().min(0),
  scheduledNotificationsOrphaned: z.number().int().min(0),
  rafflesOrphaned: z.number().int().min(0),
  clubsOrphaned: z.number().int().min(0),
  studentProfileDeleted: z.boolean(),
  postsDeleted: z.number().int().min(0),
});

const deletedUserInfoSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  role: z.enum(['student', 'student_admin', 'super_admin', 'admin']),
});

const userDeletionResultSchema = z.object({
  success: z.boolean(),
  deletedUser: deletedUserInfoSchema.optional(),
  cleanupStats: cleanupStatsSchema.optional(),
  constraintStatus: constraintStatusSchema.optional(),
  error: z.string().optional(),
});

const bulkDeletionSuccessSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  role: z.enum(['student', 'student_admin', 'super_admin', 'admin']),
  cleanupStats: cleanupStatsSchema,
});

const bulkDeletionFailureSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  error: z.string(),
});

const bulkUserDeletionResultSchema = z.object({
  totalProcessed: z.number().int().min(0),
  successCount: z.number().int().min(0),
  failureCount: z.number().int().min(0),
  successes: z.array(bulkDeletionSuccessSchema),
  failures: z.array(bulkDeletionFailureSchema),
  duration: z.string(),
  startTime: z.date(),
  endTime: z.date(),
});

export class DeleteUserWithReassignmentDto extends createZodDto(
  deleteUserWithReassignmentSchema,
) {}

export class BulkDeleteUsersDto extends createZodDto(bulkDeleteUsersSchema) {}

export class ConstraintFixesResponseDto extends createZodDto(
  constraintFixesResponseSchema,
) {}

export class ConstraintStatusDto extends createZodDto(constraintStatusSchema) {}

export class CleanupStatsDto extends createZodDto(cleanupStatsSchema) {}

export class DeletedUserInfoDto extends createZodDto(deletedUserInfoSchema) {}

export class UserDeletionResultDto extends createZodDto(
  userDeletionResultSchema,
) {}

export class BulkDeletionSuccessDto extends createZodDto(
  bulkDeletionSuccessSchema,
) {}

export class BulkDeletionFailureDto extends createZodDto(
  bulkDeletionFailureSchema,
) {}

export class BulkUserDeletionResultDto extends createZodDto(
  bulkUserDeletionResultSchema,
) {}

export class UserDeletionResponseDto {
  @ApiProperty({
    description: 'Operation success status',
    example: true,
  })
  success!: boolean;

  @ApiProperty({
    type: DeletedUserInfoDto,
    description: 'Information about the deleted user',
    required: false,
  })
  deletedUser?: DeletedUserInfoDto;

  @ApiProperty({
    type: CleanupStatsDto,
    description: 'Statistics about cleanup operations performed',
    required: false,
  })
  cleanupStats?: CleanupStatsDto;

  @ApiProperty({
    type: ConstraintStatusDto,
    description: 'Database constraint status information',
    required: false,
  })
  constraintStatus?: ConstraintStatusDto;

  @ApiProperty({
    description: 'Error message if operation failed',
    required: false,
  })
  error?: string;
}

export class BulkDeletionResponseDto {
  @ApiProperty({
    description: 'Total number of users processed',
    example: 10,
  })
  totalProcessed!: number;

  @ApiProperty({
    description: 'Number of successful deletions',
    example: 8,
  })
  successCount!: number;

  @ApiProperty({
    description: 'Number of failed deletions',
    example: 2,
  })
  failureCount!: number;

  @ApiProperty({
    type: [BulkDeletionSuccessDto],
    description: 'Details of successful deletions',
  })
  successes!: BulkDeletionSuccessDto[];

  @ApiProperty({
    type: [BulkDeletionFailureDto],
    description: 'Details of failed deletions',
  })
  failures!: BulkDeletionFailureDto[];

  @ApiProperty({
    description: 'Total operation duration',
    example: '2m 30s',
  })
  duration!: string;

  @ApiProperty({
    description: 'Operation start time',
  })
  startTime!: Date;

  @ApiProperty({
    description: 'Operation end time',
  })
  endTime!: Date;
}
