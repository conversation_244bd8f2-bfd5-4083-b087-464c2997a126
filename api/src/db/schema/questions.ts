import {
  boolean,
  pgTable,
  text,
  timestamp,
  unique,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { users } from './users';
import { questionBank } from './mcq';
import { createInsertSchema } from 'drizzle-zod';
import { relations } from 'drizzle-orm';

export const questionsSchema = pgTable(
  'questions',
  {
    id: uuid('id')
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    question: text('question').notNull(),
    option_a: text('option_a').notNull(),
    option_b: text('option_b').notNull(),
    option_c: text('option_c').notNull(),
    option_d: text('option_d').notNull(),
    answers: text('answer').notNull(),
    questionBank_id: uuid('questionBank_id')
      .notNull()
      .references(() => questionBank.id, {
        onDelete: 'cascade',
      }),
    is_golden: boolean('is_golden').default(false),
    created_by: uuid('created_by').references(() => users.id, {
      onDelete: 'set null',
    }),
    created_at: timestamp('created_at', { mode: 'string' })
      .notNull()
      .defaultNow(),
    updated_at: timestamp('updated_at', { mode: 'string' })
      .notNull()
      .default(sql`now()`)
      .$onUpdate(() => sql`now()`),
  },
  (table) => ({
    uniqueQuestionBank: unique('question_questionbank_unique').on(
      table.question,
      table.questionBank_id,
    ),
  }),
);

export const questionsRelations = relations(questionsSchema, ({ one }) => ({
  questionBank: one(questionBank, {
    fields: [questionsSchema.questionBank_id],
    references: [questionBank.id],
  }),
  createdBy: one(users, {
    fields: [questionsSchema.created_by],
    references: [users.id],
  }),
}));

export const insertQuestionsSchema = createInsertSchema(questionsSchema).omit({
  id: true,
  created_at: true,
  updated_at: true,
});
export type Questions = typeof questionsSchema.$inferSelect;
export type QuestionsInput = typeof questionsSchema.$inferInsert;
export const questionKeys = Object.keys(questionsSchema) as [
  string,
  ...string[],
];
