import { Module } from '@nestjs/common';
import { UploadService } from './upload.service';
import { UploadController } from './upload.controller';
import { MulterModule } from '@nestjs/platform-express';
import { memoryStorage } from 'multer';
import { AwsConfigService } from './aws-config.service';
import { UPLOAD_LIMITS } from './upload.config';

@Module({
  imports: [
    MulterModule.registerAsync({
      useFactory: () => ({
        storage: memoryStorage(),
        limits: {
          fileSize: UPLOAD_LIMITS.MAX_FILE_SIZE,
          files: UPLOAD_LIMITS.MAX_FILES_PER_REQUEST,
          fields: UPLOAD_LIMITS.MAX_FORM_FIELDS,
          fieldNameSize: UPLOAD_LIMITS.MAX_FIELD_NAME_SIZE,
          fieldSize: UPLOAD_LIMITS.MAX_FIELD_VALUE_SIZE,
        },
      }),
    }),
  ],
  exports: [AwsConfigService, UploadService],
  controllers: [UploadController],
  providers: [UploadService, AwsConfigService],
})
export class UploadModule {}
