import {
  UPLOAD_LIMITS,
  ALLOWED_FILE_TYPES,
  FILE_TYPE_PATTERNS,
  UPLOAD_ERROR_MESSAGES,
} from './upload.config';

describe('Upload Configuration', () => {
  describe('UPLOAD_LIMITS', () => {
    it('should have reasonable file size limits for DoS protection', () => {
      expect(UPLOAD_LIMITS.MAX_FILE_SIZE).toBe(10 * 1024 * 1024); // 10MB
      expect(UPLOAD_LIMITS.IMAGE_MAX_SIZE).toBe(2 * 1024 * 1024); // 2MB
      expect(UPLOAD_LIMITS.DOCUMENT_MAX_SIZE).toBe(5 * 1024 * 1024); // 5MB
    });

    it('should have safe request limits', () => {
      expect(UPLOAD_LIMITS.MAX_FILES_PER_REQUEST).toBe(5);
      expect(UPLOAD_LIMITS.MAX_FORM_FIELDS).toBe(10);
      expect(UPLOAD_LIMITS.MAX_FIELD_NAME_SIZE).toBe(100);
      expect(UPLOAD_LIMITS.MAX_FIELD_VALUE_SIZE).toBe(1024 * 1024); // 1MB
    });

    it('should have rate limiting configuration', () => {
      expect(UPLOAD_LIMITS.UPLOAD_RATE_LIMIT.WINDOW_MS).toBe(60 * 1000); // 1 minute
      expect(UPLOAD_LIMITS.UPLOAD_RATE_LIMIT.MAX_REQUESTS).toBe(5);
    });

    it('should ensure image size is less than max file size', () => {
      expect(UPLOAD_LIMITS.IMAGE_MAX_SIZE).toBeLessThanOrEqual(
        UPLOAD_LIMITS.MAX_FILE_SIZE,
      );
    });

    it('should ensure document size is less than max file size', () => {
      expect(UPLOAD_LIMITS.DOCUMENT_MAX_SIZE).toBeLessThanOrEqual(
        UPLOAD_LIMITS.MAX_FILE_SIZE,
      );
    });
  });

  describe('ALLOWED_FILE_TYPES', () => {
    it('should include common image types', () => {
      expect(ALLOWED_FILE_TYPES.IMAGES).toContain('image/png');
      expect(ALLOWED_FILE_TYPES.IMAGES).toContain('image/jpeg');
      expect(ALLOWED_FILE_TYPES.IMAGES).toContain('image/jpg');
    });

    it('should include document types', () => {
      expect(ALLOWED_FILE_TYPES.DOCUMENTS).toContain('text/csv');
      expect(ALLOWED_FILE_TYPES.DOCUMENTS).toContain('application/pdf');
      expect(ALLOWED_FILE_TYPES.DOCUMENTS).toContain(
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      );
      expect(ALLOWED_FILE_TYPES.DOCUMENTS).toContain(
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      );
    });

    it('should combine all types correctly', () => {
      const expectedLength =
        ALLOWED_FILE_TYPES.IMAGES.length + ALLOWED_FILE_TYPES.DOCUMENTS.length;
      expect(ALLOWED_FILE_TYPES.ALL).toHaveLength(expectedLength);

      ALLOWED_FILE_TYPES.IMAGES.forEach((type) => {
        expect(ALLOWED_FILE_TYPES.ALL).toContain(type);
      });

      ALLOWED_FILE_TYPES.DOCUMENTS.forEach((type) => {
        expect(ALLOWED_FILE_TYPES.ALL).toContain(type);
      });
    });
  });

  describe('FILE_TYPE_PATTERNS', () => {
    it('should match image file extensions', () => {
      expect(FILE_TYPE_PATTERNS.IMAGES.test('test.png')).toBe(true);
      expect(FILE_TYPE_PATTERNS.IMAGES.test('test.jpeg')).toBe(true);
      expect(FILE_TYPE_PATTERNS.IMAGES.test('test.jpg')).toBe(true);
      expect(FILE_TYPE_PATTERNS.IMAGES.test('test.gif')).toBe(false);
    });

    it('should match document file extensions', () => {
      expect(FILE_TYPE_PATTERNS.DOCUMENTS.test('test.csv')).toBe(true);
      expect(FILE_TYPE_PATTERNS.DOCUMENTS.test('test.pdf')).toBe(true);
      expect(FILE_TYPE_PATTERNS.DOCUMENTS.test('test.docx')).toBe(true);
      expect(FILE_TYPE_PATTERNS.DOCUMENTS.test('test.pptx')).toBe(true);
      expect(FILE_TYPE_PATTERNS.DOCUMENTS.test('test.txt')).toBe(false);
    });

    it('should be case insensitive', () => {
      expect(FILE_TYPE_PATTERNS.IMAGES.test('test.PNG')).toBe(true);
      expect(FILE_TYPE_PATTERNS.DOCUMENTS.test('test.PDF')).toBe(true);
    });
  });

  describe('UPLOAD_ERROR_MESSAGES', () => {
    it('should generate correct file size error message', () => {
      const message = UPLOAD_ERROR_MESSAGES.FILE_TOO_LARGE(2 * 1024 * 1024);
      expect(message).toBe('File is too large. Maximum size is 2MB');
    });

    it('should generate correct too many files error message', () => {
      const message = UPLOAD_ERROR_MESSAGES.TOO_MANY_FILES(5);
      expect(message).toBe(
        'Too many files. Maximum 5 files allowed per request.',
      );
    });

    it('should have all required error messages', () => {
      expect(UPLOAD_ERROR_MESSAGES.INVALID_FILE_TYPE).toBeDefined();
      expect(UPLOAD_ERROR_MESSAGES.EMPTY_FILE).toBeDefined();
      expect(UPLOAD_ERROR_MESSAGES.UPLOAD_FAILED).toBeDefined();
    });
  });
});
