import {
  <PERSON>,
  FileTypeValidator,
  Logger,
  ParseFilePipe,
  Post,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { UploadService } from './upload.service';
import { UseRoles } from 'nest-access-control';
import { AppClients } from '@app/shared/constants/auth.constants';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import { CustomMaxFileSizeValidator } from '@/validators/custom-max-file-size.validator';
import {
  ApiConsumes,
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiOkResponse,
  ApiInternalServerErrorResponse,
  ApiBadRequestResponse,
  ApiBody,
} from '@nestjs/swagger';
import { FileUploadRoutes } from '@app/shared/constants/file-upload.constants';
import { UPLOAD_LIMITS } from './upload.config';

@Controller({ version: '1', path: 'upload' })
@ApiTags('Upload')
export class UploadController {
  private readonly logger = new Logger(UploadController.name);
  constructor(private readonly uploadService: UploadService) {}

  @Post(FileUploadRoutes.UPLOAD_FILE)
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  @ApiOperation({
    summary: 'Upload a file',
    description:
      'Upload an image file (PNG, JPEG, JPG) with a maximum size of 2MB',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Image file to upload (PNG, JPEG, JPG)',
        },
      },
      required: ['file'],
    },
  })
  @ApiOkResponse({
    description: 'File uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'File uploaded successfully',
        },
        fileUrl: {
          type: 'string',
          example: 'https://cdn.example.com/bucket-name/123456789-image.jpg',
        },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid file format, file too large, or empty file',
  })
  @ApiInternalServerErrorResponse({
    description: 'S3 upload error or server configuration issue',
  })
  @ApiBearerAuth()
  @UseInterceptors(FileInterceptor('file'))
  @UseRoles({ resource: 'upload', action: 'create', possession: 'any' })
  async uploadFile(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({ fileType: /.(png|jpeg|jpg)$/ }),
          new CustomMaxFileSizeValidator({
            maxSize: UPLOAD_LIMITS.IMAGE_MAX_SIZE,
          }),
        ],
        fileIsRequired: true,
      }),
    )
    file: Express.Multer.File,
  ) {
    try {
      const fileUrl = await this.uploadService.uploadFileToS3(file);
      return {
        message: 'File uploaded successfully',
        fileUrl,
      };
    } catch (error) {
      this.logger.error('Upload error:', error);
      throw error;
    }
  }
}
