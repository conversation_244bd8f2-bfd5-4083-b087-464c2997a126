/**
 * Upload configuration constants for DoS protection and file handling
 *
 * These limits are designed to prevent Denial of Service attacks while
 * allowing reasonable file uploads for the application.
 */

export const UPLOAD_LIMITS = {
  MAX_FILE_SIZE: 10 * 1024 * 1024,
  IMAGE_MAX_SIZE: 2 * 1024 * 1024,
  DOCUMENT_MAX_SIZE: 5 * 1024 * 1024,

  MAX_FILES_PER_REQUEST: 5,
  MAX_FORM_FIELDS: 10,
  MAX_FIELD_NAME_SIZE: 100,
  MAX_FIELD_VALUE_SIZE: 1024 * 1024,

  UPLOAD_RATE_LIMIT: {
    WINDOW_MS: 60 * 1000,
    MAX_REQUESTS: 5,
  },
} as const;

export const ALLOWED_FILE_TYPES = {
  IMAGES: ['image/png', 'image/jpeg', 'image/jpg'],
  DOCUMENTS: [
    'text/csv',
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  ],
  get ALL() {
    return [...this.IMAGES, ...this.DOCUMENTS];
  },
};

export const FILE_TYPE_PATTERNS = {
  IMAGES: /\.(png|jpeg|jpg)$/i,
  DOCUMENTS: /\.(csv|pdf|docx|pptx)$/i,
  ALL: /\.(png|jpeg|jpg|csv|pdf|docx|pptx)$/i,
} as const;

export const UPLOAD_ERROR_MESSAGES = {
  FILE_TOO_LARGE: (maxSize: number) =>
    `File is too large. Maximum size is ${maxSize / (1024 * 1024)}MB`,
  INVALID_FILE_TYPE:
    'Invalid file type. Please upload a supported file format.',
  TOO_MANY_FILES: (maxFiles: number) =>
    `Too many files. Maximum ${maxFiles} files allowed per request.`,
  EMPTY_FILE: 'Empty file is not allowed.',
  UPLOAD_FAILED: 'File upload failed. Please try again.',
} as const;
