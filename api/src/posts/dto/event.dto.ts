import { insertPostSchema } from '@/db/schema/posts';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { format, isBefore } from 'date-fns';
import {
  booleanSchema,
  notificationDeliverTypeSchema,
  notificationRecipientsSchema,
} from './post.dto';
import { urlRegex } from '@/common/utils/zod-schema.utils';

export const createClubEventSchema = insertPostSchema
  .extend({
    title: z.string(),
    startDate: z
      .string()
      .refine((date) => !isNaN(Date.parse(date)), {
        message: 'Start date must be a valid date',
      })
      .transform((date) => format(date, 'yyyy-MM-dd HH:mm:ss.SSSSSS')),
    endDate: z
      .string()
      .refine((date) => !isNaN(Date.parse(date)), {
        message: 'End date must be a valid date',
      })
      .transform((date) => format(date, 'yyyy-MM-dd HH:mm:ss.SSSSSS')),

    startTime: z
      .string()
      .optional()
      .transform((val) => (val?.length ? val : null)),

    endTime: z
      .string()
      .optional()
      .transform((val) => (val?.length ? val : null)),
    virtualLink: z
      .string()
      .url()
      .regex(urlRegex, { message: 'Invalid virtual url' })
      .optional(),
    isGlobal: booleanSchema,
    status: z.enum(['active', 'draft', 'scheduled']),
    notificationDeliverType: notificationDeliverTypeSchema,
    notificationRecipients: notificationRecipientsSchema,
    scheduledAt: z
      .string()
      .optional()
      .refine((date) => !date || !isNaN(Date.parse(date)), {
        message: 'Scheduled at must be a valid date',
      })
      .transform((date) =>
        date ? format(date, 'yyyy-MM-dd HH:mm:ss.SSSSSS') : null,
      ),
  })
  .superRefine((data, ctx) => {
    const now = new Date();

    // Title length validation
    if (data.title.length < 5) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Title must be at least 5 characters long',
        path: ['title'],
      });
    }

    // Description length validation for active status
    if (
      data.status === 'active' &&
      (!data.description || data.description.length <= 10)
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Description must be at least 10 characters long',
        path: ['description'],
      });
    }

    // Start date validation
    if (isBefore(data.startDate, now)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Start date must be after current date',
        path: ['startDate'],
      });
    }

    // End date validation
    if (isBefore(data.endDate, now)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'End date must be after current date',
        path: ['endDate'],
      });
    }

    // End date cannot be before start date
    if (isBefore(data.endDate, data.startDate)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'End date cannot be before start date',
        path: ['endDate'],
      });
    }

    if (data.status === 'scheduled') {
      if (!data.scheduledAt) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Scheduled at is required when status is scheduled',
          path: ['scheduledAt'],
        });
      } else if (isBefore(data.scheduledAt, now)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Scheduled at must be a future date',
          path: ['scheduledAt'],
        });
      }
    }

    if (data.scheduledAt && data.status !== 'scheduled') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Status must be scheduled when scheduledAt is provided',
        path: ['status'],
      });
    }

    if (data.scheduledAt && isBefore(data.scheduledAt, now)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Scheduled at must be a future date',
        path: ['scheduledAt'],
      });
    }
  });

const updateClubEventSchema = insertPostSchema
  .extend({
    title: z.string(),
    startDate: z
      .string()
      .refine((date) => !isNaN(Date.parse(date)), {
        message: 'Start date must be a valid date',
      })
      .transform((date) => format(date, 'yyyy-MM-dd HH:mm:ss.SSSSSS')),
    endDate: z
      .string()
      .refine((date) => !isNaN(Date.parse(date)), {
        message: 'End date must be a valid date',
      })
      .transform((date) => format(date, 'yyyy-MM-dd HH:mm:ss.SSSSSS')),

    startTime: z
      .string()
      .optional()
      .transform((val) => (val?.length ? val : null)),

    endTime: z
      .string()
      .optional()
      .transform((val) => (val?.length ? val : null)),
    virtualLink: z
      .string()
      .url()
      .regex(urlRegex, { message: 'Invalid virtual url' })
      .optional(),
    isGlobal: booleanSchema,
    status: z.enum(['active', 'draft', 'scheduled']),
    notificationDeliverType: notificationDeliverTypeSchema,
    notificationRecipients: notificationRecipientsSchema,
    scheduledAt: z
      .string()
      .optional()
      .refine((date) => !date || !isNaN(Date.parse(date)), {
        message: 'Scheduled at must be a valid date',
      })
      .transform((date) =>
        date ? format(date, 'yyyy-MM-dd HH:mm:ss.SSSSSS') : null,
      ),
  })
  .superRefine((data, ctx) => {
    const now = new Date();

    // Title length validation
    if (data.title.length < 5) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Title must be at least 5 characters long',
        path: ['title'],
      });
    }

    // Description length validation for active status
    if (
      data.status === 'active' &&
      (!data.description || data.description.length <= 10)
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Description must be at least 10 characters long',
        path: ['description'],
      });
    }

    // Start date validation
    if (isBefore(data.startDate, now)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Start date must be after current date',
        path: ['startDate'],
      });
    }

    // End date validation
    if (isBefore(data.endDate, now)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'End date must be after current date',
        path: ['endDate'],
      });
    }

    // End date cannot be before start date
    if (isBefore(data.endDate, data.startDate)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'End date cannot be before start date',
        path: ['endDate'],
      });
    }

    if (data.status === 'scheduled') {
      if (!data.scheduledAt) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Scheduled at is required when status is scheduled',
          path: ['scheduledAt'],
        });
      } else if (isBefore(data.scheduledAt, now)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Scheduled at must be a future date',
          path: ['scheduledAt'],
        });
      }
    }

    if (data.scheduledAt && data.status !== 'scheduled') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Status must be scheduled when scheduledAt is provided',
        path: ['status'],
      });
    }

    if (data.scheduledAt && isBefore(data.scheduledAt, now)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Scheduled at must be a future date',
        path: ['scheduledAt'],
      });
    }
  });

export class ClubEventDto extends createZodDto(createClubEventSchema) {}
export class UpdateClubEventDto extends createZodDto(updateClubEventSchema) {}
