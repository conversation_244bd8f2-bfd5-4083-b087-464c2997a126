import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import {
  commonInsertEventOrOpportunitySchema,
  createArrayOfNumbersSchema,
  notificationDeliverTypeSchema,
  notificationRecipientsSchema,
} from './post.dto';
import { isBefore } from 'date-fns';
import { urlRegex } from '@/common/utils/zod-schema.utils';

const createOpportunitySchema = commonInsertEventOrOpportunitySchema
  .extend({
    applicationUrl: z
      .string()
      .regex(urlRegex, {
        message: 'Invalid application URL format',
      })
      .optional()
      .describe('URL where users can apply for the opportunity'),

    eligibility: createArrayOfNumbersSchema('eligibility').describe(
      'Array of eligibility levels (integers 1-6)',
    ),

    status: z
      .enum(['active', 'draft', 'scheduled'], {
        errorMap: () => ({
          message: 'Status must be either "active", "draft", or "scheduled"',
        }),
      })
      .describe('Status of the opportunity'),

    imageUrl: z
      .string()
      .url({ message: 'Image URL must be a valid URL' })
      .optional()
      .describe('URL of the opportunity image'),
    notificationDeliverType: notificationDeliverTypeSchema,
    notificationRecipients: notificationRecipientsSchema,
  })
  .superRefine((data, ctx) => {
    const now = new Date();

    // Title and description validation for active status
    if (data.status === 'active') {
      if (data.title.length < 5) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Title must be at least 5 characters long',
          path: ['title'],
        });
      }

      if (!data.description || data.description.length < 10) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Description must be at least 10 characters long',
          path: ['description'],
        });
      }
    }

    // Eligibility validation
    if (data.eligibility.length > 0) {
      const invalidLevels = data.eligibility.filter(
        (num: number) => !Number.isInteger(num) || num < 1 || num > 6,
      );
      if (invalidLevels.length > 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Eligibility levels must be integers between 1 and 6',
          path: ['eligibility'],
        });
      }
    }

    // Validation for non-global opportunities
    if (
      !data.isGlobal &&
      data.countries.length === 0 &&
      data.institutions.length === 0
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message:
          'For non-global opportunities, you must specify at least one country or institution',
        path: ['countries'],
      });
    }

    // Validation for global opportunities
    if (
      data.isGlobal &&
      (data.countries.length > 0 || data.institutions.length > 0)
    ) {
      if (data.countries.length > 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Global opportunities should not have countries specified',
          path: ['countries'],
        });
      }

      if (data.institutions.length > 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message:
            'Global opportunities should not have institutions specified',
          path: ['institutions'],
        });
      }
    }

    // Date validations
    if (isBefore(data.startDate, now)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Start date must be after current date',
        path: ['startDate'],
      });
    }

    if (isBefore(data.endDate, now)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'End date must be after current date',
        path: ['endDate'],
      });
    }

    if (isBefore(data.endDate, data.startDate)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'End date cannot be before start date',
        path: ['endDate'],
      });
    }

    // Application URL required for active opportunities
    if (data.status === 'active' && !data.applicationUrl) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Application url is required for active opportunities',
        path: ['applicationUrl'],
      });
    }

    if (data.status === 'scheduled') {
      if (!data.scheduledAt) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Scheduled at is required when status is scheduled',
          path: ['scheduledAt'],
        });
      } else if (isBefore(data.scheduledAt, now)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Scheduled at must be a future date',
          path: ['scheduledAt'],
        });
      }
    }

    if (data.scheduledAt && data.status !== 'scheduled') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Status must be scheduled when scheduledAt is provided',
        path: ['status'],
      });
    }

    if (data.scheduledAt && isBefore(data.scheduledAt, now)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Scheduled at must be a future date',
        path: ['scheduledAt'],
      });
    }
  });

const updateOpportunitySchema = commonInsertEventOrOpportunitySchema
  .extend({
    applicationUrl: z
      .string()
      .regex(urlRegex, {
        message: 'Invalid application URL format',
      })
      .optional()
      .describe('URL where users can apply for the opportunity'),

    eligibility: createArrayOfNumbersSchema('eligibility').describe(
      'Array of eligibility levels (integers 1-6)',
    ),

    status: z
      .enum(['active', 'draft', 'scheduled'], {
        errorMap: () => ({
          message: 'Status must be either "active", "draft", or "scheduled"',
        }),
      })
      .describe('Status of the opportunity'),

    imageUrl: z
      .string()
      .url({ message: 'Image URL must be a valid URL' })
      .optional()
      .describe('URL of the opportunity image'),
    notificationDeliverType: notificationDeliverTypeSchema,
    notificationRecipients: notificationRecipientsSchema,
  })
  .superRefine((data, ctx) => {
    const now = new Date();

    // Title and description validation for active status
    if (data.status === 'active') {
      if (data.title.length < 5) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Title must be at least 5 characters long',
          path: ['title'],
        });
      }

      if (!data.description || data.description.length < 10) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Description must be at least 10 characters long',
          path: ['description'],
        });
      }
    }

    // Eligibility validation
    if (data.eligibility.length > 0) {
      const invalidLevels = data.eligibility.filter(
        (num: number) => !Number.isInteger(num) || num < 1 || num > 6,
      );
      if (invalidLevels.length > 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Eligibility levels must be integers between 1 and 6',
          path: ['eligibility'],
        });
      }
    }

    // Validation for non-global opportunities
    if (
      !data.isGlobal &&
      data.countries.length === 0 &&
      data.institutions.length === 0
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message:
          'For non-global opportunities, you must specify at least one country or institution',
        path: ['countries'],
      });
    }

    // Validation for global opportunities
    if (
      data.isGlobal &&
      (data.countries.length > 0 || data.institutions.length > 0)
    ) {
      if (data.countries.length > 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Global opportunities should not have countries specified',
          path: ['countries'],
        });
      }

      if (data.institutions.length > 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message:
            'Global opportunities should not have institutions specified',
          path: ['institutions'],
        });
      }
    }

    // Date validations
    if (isBefore(data.startDate, now)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Start date must be after current date',
        path: ['startDate'],
      });
    }

    if (isBefore(data.endDate, now)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'End date must be after current date',
        path: ['endDate'],
      });
    }

    if (isBefore(data.endDate, data.startDate)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'End date cannot be before start date',
        path: ['endDate'],
      });
    }

    // Application URL required for active opportunities
    if (data.status === 'active' && !data.applicationUrl) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Application url is required for active opportunities',
        path: ['applicationUrl'],
      });
    }

    // scheduledAt validation based on status
    if (data.status === 'scheduled') {
      if (!data.scheduledAt) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Scheduled at is required when status is scheduled',
          path: ['scheduledAt'],
        });
      } else if (isBefore(data.scheduledAt, now)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Scheduled at must be a future date',
          path: ['scheduledAt'],
        });
      }
    }

    // If scheduledAt is provided but status is not scheduled, it should be scheduled
    if (data.scheduledAt && data.status !== 'scheduled') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Status must be scheduled when scheduledAt is provided',
        path: ['status'],
      });
    }

    // General scheduledAt validation: must be after now if provided
    if (data.scheduledAt && isBefore(data.scheduledAt, now)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Scheduled at must be a future date',
        path: ['scheduledAt'],
      });
    }
  });

export class OpportunityDto extends createZodDto(createOpportunitySchema) {}
export class UpdateOpportunityDto extends createZodDto(
  updateOpportunitySchema,
) {}
