import {
  Controller,
  Post,
  Body,
  Logger,
  Get,
  UseInterceptors,
  UploadedFiles,
  ParseFilePipeBuilder,
  Query,
  Delete,
  UseGuards,
  Param,
  Patch,
  Put,
} from '@nestjs/common';
import { PostService } from './post.service';
import { ZodSerializerDto } from 'nestjs-zod';
import {
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiConsumes,
  ApiNotFoundResponse,
  ApiUnauthorizedResponse,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import {
  PostDto,
  PostEngagementParamsDto,
  PostQueryParamsDto,
} from './dto/post.dto';
import { User } from '@/guards/user.decorator';
import { Post as IPost, type User as UserDecoratorType } from '@/db/schema';
import {
  PostControllerMessages,
  PostRoutes,
  PostServiceMessages,
} from '@app/shared/constants/post.constants';
import { UseRoles } from 'nest-access-control';
import { FilesInterceptor } from '@nestjs/platform-express';
import { CustomMaxFileSizeValidator } from '@/validators/custom-max-file-size.validator';
import { RoleGuard } from '@/guards/role.guard';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import { AppClients } from '@app/shared/constants/auth.constants';
import { CustomFileUploadValidator } from '@/validators/custom-file-type.validator';
import { CustomParseUUIDPipe } from '@/common/pipes/custom-parse-uuid';

@ApiTags('Posts')
@Controller({ version: '1', path: 'post' })
export class PostController {
  private readonly logger = new Logger(PostService.name);
  constructor(private readonly postService: PostService) {}

  @Post()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'post', action: 'create', possession: 'any' })
  @ZodSerializerDto(PostDto)
  @ApiOperation({
    summary: 'Create a new post',
    description:
      'Create a new post with optional attachments and notification settings',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: PostDto,
    description: 'Post data to create',
  })
  @ApiCreatedResponse({
    description: 'The record has been successfully created.',
    type: PostDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid post data',
  })
  @UseInterceptors(FilesInterceptor('attachments'))
  async createPost(
    @User() user: UserDecoratorType,
    @Body() data: PostDto,
    @UploadedFiles(
      new ParseFilePipeBuilder()
        .addValidator(
          new CustomMaxFileSizeValidator({
            maxSize: 2 * 1024 * 1024,
          }),
        )
        .addValidator(new CustomFileUploadValidator({ isRequired: false }))
        .build({ fileIsRequired: false }),
    )
    attachments: Express.Multer.File[] = [],
  ) {
    try {
      return await this.postService.createPost(data, user, attachments);
    } catch (error: any) {
      this.logger.error('Error creating post', error.stack);
      throw error;
    }
  }

  @Post(PostRoutes.CREATE_CLUB_SPECIFIC_POST)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'post', action: 'create', possession: 'any' })
  @ZodSerializerDto(PostDto)
  @ApiOperation({
    summary: 'Create club-specific post',
    description: 'Create a new post associated with a specific club',
  })
  @ApiConsumes('multipart/form-data')
  @ApiParam({
    name: 'clubId',
    description: 'Club unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiBody({
    type: PostDto,
    description: 'Post data to create',
  })
  @ApiCreatedResponse({
    description: 'The record has been successfully created.',
    type: PostDto,
  })
  @ApiNotFoundResponse({
    description: PostControllerMessages.PostNotFound,
  })
  @ApiUnauthorizedResponse({
    description: PostServiceMessages.UnAuthorized,
  })
  @UseInterceptors(FilesInterceptor('attachments'))
  async createClubGeneralPost(
    @User() user: UserDecoratorType,
    @Body() data: PostDto,
    @Param('clubId', new CustomParseUUIDPipe()) clubId: string,
    @UploadedFiles(
      new ParseFilePipeBuilder()
        .addValidator(
          new CustomMaxFileSizeValidator({
            maxSize: 2 * 1024 * 1024,
          }),
        )
        .addValidator(new CustomFileUploadValidator({ isRequired: false }))
        .build({ fileIsRequired: false }),
    )
    attachments: Express.Multer.File[] = [],
  ) {
    try {
      return await this.postService.createClubGeneralPost(
        data,
        user,
        clubId,
        attachments,
      );
    } catch (error: any) {
      this.logger.error('Error creating club general post', error.stack);
      throw error;
    }
  }

  @Put(PostRoutes.UPDATE_POST)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'post', action: 'update', possession: 'any' })
  @ZodSerializerDto(PostDto)
  @ApiOperation({
    summary: 'Update post',
    description: 'Update an existing post by ID',
  })
  @ApiConsumes('multipart/form-data')
  @ApiParam({
    name: 'id',
    description: 'Post unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiBody({
    type: PostDto,
    description: 'Updated post data',
  })
  @ApiCreatedResponse({
    description: 'The record has been successfully updated.',
    type: PostDto,
  })
  @ApiNotFoundResponse({
    description: PostControllerMessages.PostNotFound,
  })
  @ApiUnauthorizedResponse({
    description: PostServiceMessages.UnAuthorized,
  })
  @UseInterceptors(FilesInterceptor('attachments'))
  async updatePost(
    @User() user: UserDecoratorType,
    @Body() data: PostDto,
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @UploadedFiles(
      new ParseFilePipeBuilder()
        .addValidator(
          new CustomMaxFileSizeValidator({
            maxSize: 2 * 1024 * 1024,
          }),
        )
        .addValidator(new CustomFileUploadValidator({ isRequired: false }))
        .build({ fileIsRequired: false }),
    )
    attachments: Express.Multer.File[],
  ) {
    try {
      return await this.postService.updatePost(
        {
          title: data.title,
          description: data.description,
          imageUrl: data?.imageUrl,
          status: data.status,
        },
        id,
        user,
        attachments,
      );
    } catch (error: any) {
      this.logger.error('Error updating post', error.stack);
      throw error;
    }
  }

  @Get()
  @ApiOperation({
    summary: 'Get all posts',
    description:
      'Retrieve a list of all posts with optional filtering and pagination',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Filter posts by title or description',
    type: String,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    type: Number,
  })
  @ApiQuery({
    name: 'sort',
    required: false,
    description: 'Field to sort by',
    type: String,
  })
  @ApiQuery({
    name: 'order',
    required: false,
    description: 'Sort order (asc or desc)',
    enum: ['asc', 'desc'],
  })
  @ApiQuery({
    name: 'type',
    required: false,
    description: 'Filter by post type',
    enum: ['general', 'event', 'opportunity'],
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter by post status',
    enum: ['active', 'draft', 'pending'],
  })
  @ApiOkResponse({
    description: 'Successfully retrieved all posts.',
    type: [PostDto],
  })
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'post',
    action: 'read',
    possession: 'any',
  })
  async getPosts(
    @User() user: UserDecoratorType,
    @Query() query: PostQueryParamsDto,
  ) {
    try {
      return await this.postService.getPosts(
        user,
        query as PostQueryParamsDto & {
          sort: keyof IPost;
        },
      );
    } catch (error) {
      this.logger.error('Error retrieving list of posts', error);
      throw error;
    }
  }

  @Get(PostRoutes.GET_ONE_POST)
  @ApiOperation({
    summary: 'Get post by ID',
    description: 'Retrieve a specific post by its unique identifier',
  })
  @ApiParam({
    name: 'id',
    description: 'Post unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Successfully retrieved the post.',
    type: PostDto,
  })
  @ApiNotFoundResponse({
    description: PostControllerMessages.PostNotFound,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access to post',
  })
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'post',
    action: 'read',
    possession: 'any',
  })
  async getOnePost(
    @User() user: UserDecoratorType,
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ) {
    try {
      return await this.postService.getPostById(id, user);
    } catch (error: any) {
      this.logger.error(`Error retrieving post with ID ${id}`, error.stack);
      throw error;
    }
  }

  @Get(PostRoutes.GET_POST_ENGAGEMENTS)
  @ApiOperation({
    summary: 'Get post engagements',
    description: 'Retrieve engagement statistics for a specific post',
  })
  @ApiParam({
    name: 'id',
    description: 'Post unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    type: Number,
  })
  @ApiQuery({
    name: 'type',
    required: false,
    description: 'Type of engagement to filter by',
    enum: ['like', 'share'],
  })
  @ApiOkResponse({
    description: 'Successfully retrieved post engagements.',
    type: PostDto,
  })
  @ApiNotFoundResponse({
    description: PostControllerMessages.PostNotFound,
  })
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'post',
    action: 'read',
    possession: 'any',
  })
  async getPostEngagements(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @Query() query: PostEngagementParamsDto,
  ) {
    try {
      const postEngagements = await this.postService.getPostEngagements(
        id,
        query,
      );

      return postEngagements;
    } catch (error: any) {
      this.logger.error('Error retrieving a post engagements', error.stack);
      throw error;
    }
  }

  @Get(PostRoutes.GET_TRENDING_BY_ID)
  @ApiOperation({
    summary: 'Get trending post by ID',
    description: 'Retrieve a specific trending post by its unique identifier',
  })
  @ApiParam({
    name: 'id',
    description: 'Trending post unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'post', action: 'read', possession: 'any' })
  @ApiOkResponse({
    description: 'Successfully retrieved the trending post.',
    type: PostDto,
  })
  @ApiNotFoundResponse({
    description: 'Trending post with ID not found',
  })
  async getTrendingById(@Param('id', new CustomParseUUIDPipe()) id: string) {
    try {
      return await this.postService.getTrendingById(id);
    } catch (error: any) {
      this.logger.error(
        `Error retrieving trending post with ID ${id}`,
        error.stack,
      );
      throw error;
    }
  }

  @Delete(PostRoutes.DELETE_POST)
  @ApiOperation({
    summary: 'Delete post',
    description: 'Delete a post by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Post unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'post', action: 'delete', possession: 'any' })
  @ApiNoContentResponse({
    description: 'Post deleted',
  })
  @ApiNotFoundResponse({
    description: PostControllerMessages.PostNotFound,
  })
  @ApiUnauthorizedResponse({
    description: PostServiceMessages.UnAuthorized,
  })
  @CLIENT_TYPE(AppClients.WEB)
  async deletePost(
    @Param('id', new CustomParseUUIDPipe()) postId: string,
    @User() user: UserDecoratorType,
  ) {
    try {
      await this.postService.deletePost(postId, user);
    } catch (error: any) {
      this.logger.error('error deleting a post', error);
      throw error;
    }
  }

  @Patch(PostRoutes.DISABLE_POST)
  @ApiOperation({
    summary: 'Disable post',
    description: 'Disable a post by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Post unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'post', action: 'update', possession: 'any' })
  @ApiOkResponse({
    description: 'Post disabled successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Post disabled successfully',
        },
      },
    },
  })
  @ApiNotFoundResponse({
    description: PostControllerMessages.PostNotFound,
  })
  @ApiUnauthorizedResponse({
    description: PostServiceMessages.UnAuthorized,
  })
  @CLIENT_TYPE(AppClients.WEB)
  async disablePost(
    @Param('id', new CustomParseUUIDPipe()) postId: string,
    @User() user: UserDecoratorType,
  ) {
    try {
      await this.postService.updatePost(
        {
          disabled: true,
        },
        postId,
        user,
      );
      return {
        message: 'Post disabled successfully',
      };
    } catch (error: any) {
      this.logger.error('error disabling a post', error);
      throw error;
    }
  }

  @Patch(PostRoutes.ENABLE_POST)
  @ApiOperation({
    summary: 'Enable post',
    description: 'Enable a previously disabled post by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Post unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'post', action: 'update', possession: 'any' })
  @ApiOkResponse({
    description: 'Post enabled successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Post enabled successfully',
        },
      },
    },
  })
  @ApiNotFoundResponse({
    description: PostControllerMessages.PostNotFound,
  })
  @ApiUnauthorizedResponse({
    description: PostServiceMessages.UnAuthorized,
  })
  @CLIENT_TYPE(AppClients.WEB)
  async enablePost(
    @Param('id', new CustomParseUUIDPipe()) postId: string,
    @User() user: UserDecoratorType,
  ) {
    try {
      await this.postService.updatePost(
        {
          disabled: false,
        },
        postId,
        user,
      );
      return {
        message: 'Post enabled successfully',
      };
    } catch (error: any) {
      this.logger.error('error enabling a post', error);
      throw error;
    }
  }

  @Get(PostRoutes.GET_CLUB_POSTS)
  @ApiOperation({
    summary: 'Get club posts',
    description: 'Retrieve posts associated with a specific club',
  })
  @ApiParam({
    name: 'id',
    description: 'Club unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Filter posts by title or description',
    type: String,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    type: Number,
  })
  @ApiQuery({
    name: 'type',
    required: false,
    description: 'Filter by post type',
    enum: ['general', 'event', 'opportunity'],
  })
  @ApiOkResponse({
    description: 'Successfully retrieved all posts.',
    type: [PostDto],
  })
  @ApiNotFoundResponse({
    description: PostServiceMessages.StudentNotAClubMember,
  })
  @UseGuards(RoleGuard)
  @UseRoles({
    resource: 'post',
    action: 'read',
    possession: 'any',
  })
  async getClubPosts(
    @User() user: UserDecoratorType,
    @Query() query: PostQueryParamsDto,
    @Param('id', new CustomParseUUIDPipe()) clubId: string,
  ) {
    try {
      return await this.postService.getClubPosts(
        user,
        query as PostQueryParamsDto & {
          sort: keyof IPost;
        },
        clubId,
      );
    } catch (error) {
      this.logger.error('Error retrieving list of posts for club', error);
      throw error;
    }
  }

  @Post(PostRoutes.LIKE_POST)
  @ApiOperation({
    summary: 'Like post',
    description: 'Like a post (or unlike if already liked)',
  })
  @ApiParam({
    name: 'id',
    description: 'Post unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'likes', action: 'create', possession: 'any' })
  @ApiOkResponse({
    description: 'Post liked successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Post liked successfully',
        },
      },
    },
  })
  @ApiNotFoundResponse({
    description: PostControllerMessages.PostNotFound,
  })
  @CLIENT_TYPE(AppClients.MOBILE)
  async likePost(
    @Param('id', new CustomParseUUIDPipe()) postId: string,
    @User() user: UserDecoratorType,
  ) {
    try {
      await this.postService.likePost(postId, user);
      return {
        message: 'Post liked successfully',
      };
    } catch (error: any) {
      this.logger.error('error liking a post', error);
      throw error;
    }
  }

  @Post(PostRoutes.SHARE_POST)
  @ApiOperation({
    summary: 'Share post',
    description: 'Share a post with others',
  })
  @ApiParam({
    name: 'id',
    description: 'Post unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'shares', action: 'create', possession: 'any' })
  @ApiOkResponse({
    description: 'Post shared successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Post shared successfully',
        },
      },
    },
  })
  @ApiNotFoundResponse({
    description: PostControllerMessages.PostNotFound,
  })
  @CLIENT_TYPE(AppClients.MOBILE)
  async sharePost(
    @Param('id', new CustomParseUUIDPipe()) postId: string,
    @User() user: UserDecoratorType,
  ) {
    try {
      await this.postService.sharePost(postId, user);
      return {
        message: 'Post shared successfully',
      };
    } catch (error: any) {
      this.logger.error('error sharing a post', error);
      throw error;
    }
  }
}
