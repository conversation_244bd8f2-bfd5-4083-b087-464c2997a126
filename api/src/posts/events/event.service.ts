import {
  events,
  posts,
  post_types,
  postCountries,
  postInstitutions,
  post_statuses,
  postImages,
} from '@/db/schema/posts';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { Cacheable } from '@app/shared/cache/decorators/cacheable.decorator';
import { EventDto } from '../dto/post.dto';
import { PostRepository } from '@/repositories/post.repository';
import {
  countries,
  institutions,
  student_clubs,
  User,
  user_roles,
} from '@/db/schema';
import { eq, inArray } from 'drizzle-orm';
import { PostServiceMessages } from '@app/shared/constants/post.constants';
import { ClubEventDto } from '../dto/event.dto';

import { PostService } from '../post.service';
import { CacheService } from '@app/shared/redis/cache.service';
import {
  CACHE_PREFIXES,
  CACHE_TTL,
} from '@app/shared/constants/cache.constant';
import {
  generateEventKey,
  invalidateEventCaches,
  invalidatePostCachesForEvent,
} from '../utils/unified-cache.utils';

@Injectable()
export class EventService {
  private readonly logger = new Logger(EventService.name);
  private readonly EVENT_CACHE_PREFIX = CACHE_PREFIXES.EVENT;
  private readonly POST_CACHE_PREFIX = CACHE_PREFIXES.POST;
  private readonly CACHE_TTL = CACHE_TTL.ONE_DAY;

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly postRepository: PostRepository,
    private readonly postService: PostService,
    private readonly cacheService: CacheService,
  ) {}

  async createEvent(
    data: EventDto,
    user: User,
    attachements: Express.Multer.File[] = [],
  ) {
    const { status, countries, institutions, notify_users, ...sanitizedData } =
      data;

    if (countries.length) {
      await this.checkNonExistentCountries(countries);
    }

    if (institutions.length) {
      await this.checkNonExistentInstitutions(institutions);
    }

    const createdPostData = await this.drizzle.db.transaction(async (tx) => {
      const [createdPost] = await this.postRepository.createPost(
        {
          ...sanitizedData,
          postedBy: user.id,
          type: post_types.EVENT,
          isGlobal: sanitizedData.isGlobal || false,
          status: attachements.length
            ? post_statuses.PENDING
            : (status ?? post_statuses.PENDING),
          notify_users: notify_users ?? Boolean(data.scheduledAt),
          scheduledAt: sanitizedData.scheduledAt ?? null,
        },
        tx,
      );
      if (!createdPost) {
        throw new InternalServerErrorException('Failed to create post');
      }

      const [createdEvent] = await tx
        .insert(events)
        .values({
          postId: createdPost.id,
          startDate: sanitizedData.startDate,
          endDate: sanitizedData.endDate,
          startTime: sanitizedData.startTime,
          endTime: sanitizedData.endTime,
          virtualLink: sanitizedData.virtualLink,
        })
        .returning();

      countries.length &&
        (await tx.insert(postCountries).values(
          countries?.map((countryId: string) => ({
            countryId,
            postId: createdPost?.id,
          })),
        ));

      institutions.length &&
        (await tx.insert(postInstitutions).values(
          institutions?.map((institutionId: string) => ({
            institutionId,
            postId: createdPost?.id,
          })),
        ));

      return { post: createdPost, event: createdEvent };
    });

    if (!createdPostData)
      throw new InternalServerErrorException('Failed to create event');

    if (attachements.length)
      await this.postService.uploadPostAttachments(
        createdPostData.post.id,
        attachements,
        status,
      );

    // Send notification if needed
    const shouldNotify =
      (data.notificationDeliverType || notify_users) &&
      createdPostData.post.status !== post_statuses.DRAFT &&
      createdPostData.post.status !== post_statuses.SCHEDULED;

    if (shouldNotify) {
      try {
        // Use the PostService's sendPostNotifications method
        await this.postService.sendPostNotifications(
          createdPostData.post,
          data,
        );
        this.logger.log(
          `Sent notification for event ${createdPostData.post.id}`,
        );
      } catch (error) {
        this.logger.error(
          `Failed to send notification for event ${createdPostData.post.id}`,
          error,
        );
      }
    }

    // Fetch the complete event
    const completeEvent = await this.postService.getPostById(
      createdPostData.post.id,
      user,
    );

    try {
      // Cache the created event
      await this.cacheService.set(
        generateEventKey(
          this.cacheService,
          createdPostData.post.id,
          this.EVENT_CACHE_PREFIX,
        ),
        completeEvent,
        this.CACHE_TTL,
      );

      // Invalidate relevant caches in parallel
      await Promise.all([
        invalidateEventCaches(
          this.cacheService,
          createdPostData.post.id,
          this.EVENT_CACHE_PREFIX,
        ),
        invalidatePostCachesForEvent(
          this.cacheService,
          createdPostData.post.id,
          this.POST_CACHE_PREFIX,
        ),
      ]);
    } catch (error) {
      this.logger.warn('Failed to update event cache', error);
    }

    return completeEvent;
  }

  async createClubEvent(
    data: ClubEventDto,
    user: User,
    clubId: string,
    attachements: Express.Multer.File[] = [],
  ) {
    const club = await this.drizzle.db
      .select()
      .from(student_clubs)
      .where(eq(student_clubs.id, clubId))
      .then((data) => data[0]);

    if (!club) throw new NotFoundException(PostServiceMessages.ClubNotFound);

    if (user.role === user_roles.STUDENT_ADMIN && club.club_admin !== user.id)
      throw new UnauthorizedException(PostServiceMessages.UnAuthorized);

    // Create a sanitized version of the data without the notify_users field
    const { notify_users, ...sanitizedData } = data;

    const createdEventData = await this.drizzle.db.transaction(async (tx) => {
      const [createdPost] = await this.postRepository.createPost(
        {
          ...sanitizedData,
          postedBy: user.id,
          type: post_types.EVENT,
          club_id: clubId,
          isGlobal: sanitizedData.isGlobal ?? false,
          status: attachements.length
            ? post_statuses.PENDING
            : post_statuses.DRAFT,
          notify_users: notify_users ?? Boolean(data.scheduledAt),
          scheduledAt: sanitizedData.scheduledAt ?? null,
        },
        tx,
      );
      if (!createdPost) {
        throw new InternalServerErrorException('Failed to create post');
      }

      await tx.insert(events).values({
        postId: createdPost.id,
        startDate: data.startDate,
        endDate: data.endDate,
        startTime: data.startTime,
        endTime: data.endTime,
        virtualLink: data.virtualLink,
      });

      await tx.insert(postCountries).values({
        countryId: club.country_id,
        postId: createdPost.id,
      });

      await tx.insert(postInstitutions).values({
        institutionId: club.institution_id,
        postId: createdPost.id,
      });

      if (attachements.length) {
        await this.postService.uploadPostAttachments(
          createdPost.id,
          attachements,
          sanitizedData.status,
        );
      }
      return createdPost;
    });

    if (!createdEventData)
      throw new InternalServerErrorException('Failed to create club event');
    // Send notification if needed
    const shouldNotify =
      (data.notificationDeliverType || notify_users) &&
      createdEventData.status !== post_statuses.DRAFT &&
      createdEventData.status !== post_statuses.SCHEDULED;

    if (shouldNotify) {
      try {
        // Use the PostService's sendPostNotifications method
        await this.postService.sendPostNotifications(createdEventData, data);
        this.logger.log(
          `Sent notification for club event ${createdEventData.id}`,
        );
      } catch (error) {
        this.logger.error(
          `Failed to send notification for club event ${createdEventData.id}`,
          error,
        );
      }
    }

    try {
      // Cache the created club event
      await this.cacheService.set(
        generateEventKey(
          this.cacheService,
          createdEventData.id,
          this.EVENT_CACHE_PREFIX,
        ),
        createdEventData,
        this.CACHE_TTL,
      );

      // Invalidate relevant caches in parallel
      await Promise.all([
        invalidateEventCaches(
          this.cacheService,
          createdEventData.id,
          this.EVENT_CACHE_PREFIX,
        ),
        invalidatePostCachesForEvent(
          this.cacheService,
          createdEventData.id,
          this.POST_CACHE_PREFIX,
        ),
      ]);
    } catch (error) {
      this.logger.warn('Failed to update club event cache', error);
    }

    return createdEventData;
  }

  async updateEvent(
    {
      data,
      user,
      attachments,
    }: {
      data: EventDto;
      user: User;
      attachments: Express.Multer.File[];
    },
    id: string,
  ) {
    const { countries, institutions, ...eventData } = data;

    if (countries.length) {
      await this.checkNonExistentCountries(countries);
    }

    if (institutions.length) {
      await this.checkNonExistentInstitutions(institutions);
    }
    const [existingPost] = await this.drizzle.db
      .select()
      .from(posts)
      .where(eq(posts.id, id));
    if (!existingPost)
      throw new NotFoundException(PostServiceMessages.PostNotFound);
    if (existingPost.postedBy !== user.id && user.role !== user_roles.ADMIN)
      throw new UnauthorizedException(PostServiceMessages.UnAuthorized);

    const updatedEvent = await this.drizzle.db.transaction(async (tx) => {
      const [updatedPost] = await this.postRepository.updatePost(
        {
          description: eventData.description,
          title: eventData.title,
          type: post_types.EVENT,
          status: attachments.length ? post_statuses.PENDING : eventData.status,
          isGlobal: eventData.isGlobal ?? false,
          scheduledAt: eventData.scheduledAt ?? null,
        },
        id,
      );

      if (!updatedPost)
        throw new InternalServerErrorException('Failed to update event');

      await tx.delete(postCountries).where(eq(postCountries.postId, id));
      await tx.delete(postInstitutions).where(eq(postInstitutions.postId, id));
      await tx.delete(postImages).where(eq(postImages.postId, id));
      countries.length &&
        (await tx.insert(postCountries).values(
          countries?.map((countryId: string) => ({
            countryId,
            postId: id,
          })),
        ));

      institutions.length &&
        (await tx.insert(postInstitutions).values(
          institutions?.map((institutionId: string) => ({
            institutionId,
            postId: id,
          })),
        ));

      attachments.length &&
        (await this.postService.uploadPostAttachments(
          id,
          attachments,
          eventData.status,
        ));

      const updatedEvent = await tx
        .update(events)
        .set({
          startDate: eventData.startDate,
          endDate: eventData.endDate,
          startTime: eventData.startTime,
          endTime: eventData.endTime,
          virtualLink: eventData.virtualLink,
        })
        .where(eq(events.postId, id))
        .returning();

      return updatedEvent;
    });

    try {
      // Invalidate relevant caches in parallel
      await Promise.all([
        invalidateEventCaches(this.cacheService, id, this.EVENT_CACHE_PREFIX),
        invalidatePostCachesForEvent(
          this.cacheService,
          id,
          this.POST_CACHE_PREFIX,
        ),
      ]);

      // Update the specific event cache with new data
      if (updatedEvent.length > 0) {
        await this.cacheService.set(
          generateEventKey(this.cacheService, id, this.EVENT_CACHE_PREFIX),
          updatedEvent[0],
          this.CACHE_TTL,
        );
      }
    } catch (error) {
      this.logger.warn('Failed to update event caches', error);
    }

    return updatedEvent;
  }

  /**
   * Get event by ID (cached for 1 day)
   */
  @Cacheable({
    prefix: CACHE_PREFIXES.EVENT,
    ttl: CACHE_TTL.ONE_DAY,
    keyGenerator: (args) => [args[0]],
    condition: (args) => !!args[0],
  })
  async getEventById(id: string) {
    this.logger.debug(`Fetching event ${id} from database (not cache)`);

    const event = await this.drizzle.db.query.events.findFirst({
      where: eq(events.id, id),
      with: {
        post: {
          with: {
            postedBy: {
              with: {
                profile: {
                  columns: {
                    id: true,
                    email: true,
                    name: true,
                  },
                },
                student_profile: {
                  columns: {
                    id: true,
                    first_name: true,
                    last_name: true,
                  },
                },
              },
            },
            countries: {
              with: {
                country: true,
              },
            },
            institutions: {
              with: {
                institution: true,
              },
            },
            postEngagements: true,
            images: true,
            club: true,
          },
        },
      },
    });

    if (!event) {
      throw new NotFoundException(`Event with ID ${id} not found`);
    }

    return event;
  }

  private async checkNonExistentCountries(countriesData: string[]) {
    const existingCountries = await this.drizzle.db
      .select({ id: countries.id })
      .from(countries)
      .where(inArray(countries.id, countriesData))
      .then((data) => data.map((country) => country.id));

    const nonExistentCountries = countriesData.filter(
      (id) => !existingCountries.includes(id),
    );
    if (nonExistentCountries.length)
      throw new BadRequestException(
        `Some country Ids don't exist: ${nonExistentCountries}`,
      );
  }

  private async checkNonExistentInstitutions(institutionData: string[]) {
    const existingInstitutions = await this.drizzle.db
      .select({ id: institutions.id })
      .from(institutions)
      .where(inArray(institutions.id, institutionData))
      .then((data) => data.map((institution) => institution.id));

    const nonExistentInstitutions = institutionData.filter(
      (id) => !existingInstitutions.includes(id),
    );
    if (nonExistentInstitutions.length)
      throw new BadRequestException(
        `Some instutition Ids don't exist: ${nonExistentInstitutions}`,
      );
  }
}
