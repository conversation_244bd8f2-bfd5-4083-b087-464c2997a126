import { ClubController } from './club.controller';
import { ClubService } from './club.service';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { UploadService } from '@/upload/upload.service';
import { StudentProfileService } from '@/student_profile/student_profile.service';
import type { User } from '@/db/schema';
import { Request } from 'express';

// Mock the CLIENT_TYPE decorator
jest.mock('@/guards/request-validation.decorator', () => ({
  CLIENT_TYPE: () => jest.fn(),
  CLIENT_TYPE_KEY: 'CLIENT_TYPE_KEY',
}));

// Mock the Logger constructor
jest.mock('@nestjs/common', () => {
  const original = jest.requireActual('@nestjs/common');
  return {
    ...original,
    Logger: jest.fn().mockImplementation(() => ({
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    })),
  };
});

describe('ClubController', () => {
  let controller: ClubController;
  let service: ClubService;
  let uploadService: UploadService;

  const mockUser: User = {
    id: 'user-123',
    email: '<EMAIL>',
    role: 'admin',
    state: 'active',
    profile_pic_url: null,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    deleted: false,
    deleted_at: null,
  };

  const mockUserWithStudentProfile = {
    ...mockUser,
    student_profile: {
      id: 'student-profile-123',
    },
  } as any;

  const mockAuthenticatedStudent = {
    ...mockUser,
    student_profile: {
      id: 'student-profile-123',
      user_id: 'user-123',
      first_name: 'John',
      last_name: 'Doe',
      other_name: null,
      username: 'john_doe',
      phone_number: '+1234567890',
      date_of_birth: '1990-01-01',
      institution_id: 'inst-123',
      country_id: 'country-123',
      level: '100',
      degree: 'Bachelors',
      programme: 'ICT',
      github_profile: null,
      linkedin_profile: null,
      about: null,
      club_id: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      username_last_updated: new Date().toISOString(),
      deleted: false,
      deleted_at: null,
    },
  } as any;

  beforeEach(async () => {
    // Create mock services
    const mockClubService = {
      addStudentClub: jest.fn(),
      updateStudentClub: jest.fn(),
      removeStudentClub: jest.fn(),
      getClubById: jest.fn(),
      getAllClubs: jest.fn(),
      getActiveClubs: jest.fn(),
      getInactiveClubs: jest.fn(),
      getClubMembersByClubId: jest.fn(),
      addStudentToClub: jest.fn(),
      setClubStatus: jest.fn(),
      getStudentClubByStudentId: jest.fn(),
      getClubMembersByInstitutionId: jest.fn(),
      updateStudentMembershipRole: jest.fn(),
      removeStudentFromClub: jest.fn(),
      addIsMemberFlagToClubs: jest.fn(),
    };

    const mockUploadService = {
      uploadFileToS3: jest.fn(),
    };

    const mockStudentProfileService = {
      getStudentProfileById: jest.fn(),
    };

    // Create the controller directly
    controller = new ClubController(
      mockClubService as unknown as ClubService,
      mockUploadService as unknown as UploadService,
      mockStudentProfileService as unknown as StudentProfileService,
    );

    // Assign the mocks to the test variables
    service = mockClubService as unknown as ClubService;
    uploadService = mockUploadService as unknown as UploadService;
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('addStudentClub', () => {
    it('should call validateClientType and addStudentClub successfully', async () => {
      const clubInput: any = {
        name: 'Test Club',
        description: 'Test Description',
        institution_id: 'inst-123',
        country_id: 'country-123',
      };
      const result = { id: '1', ...clubInput };

      (service.addStudentClub as jest.Mock).mockResolvedValue(result);

      const response = await controller.addStudentClub(mockUser, clubInput);

      expect(service.addStudentClub).toHaveBeenCalledWith(
        clubInput,
        mockUser.id,
      );
      expect(response).toEqual(result);
    });

    it('should handle errors', async () => {
      const clubInput: any = { name: 'Test Club' };
      const error = new Error('Failed to add student club');

      (service.addStudentClub as jest.Mock).mockRejectedValue(error);

      try {
        await controller.addStudentClub(mockUser, clubInput);
        // If we reach here, the test should fail
        expect(true).toBe(false);
      } catch (e) {
        expect(e).toBe(error);
      }
    });

    it('should handle validation errors', async () => {
      const clubInput: any = { name: '' };
      const error = new BadRequestException('Validation failed');

      (service.addStudentClub as jest.Mock).mockRejectedValue(error);

      await expect(
        controller.addStudentClub(mockUser, clubInput),
      ).rejects.toThrow(error);
    });

    it('should handle database constraint errors', async () => {
      const clubInput: any = {
        name: 'Test Club',
        description: 'Test Description',
        institution_id: 'inst-123',
        country_id: 'country-123',
      };
      const error = new BadRequestException('Club name already exists');

      (service.addStudentClub as jest.Mock).mockRejectedValue(error);

      await expect(
        controller.addStudentClub(mockUser, clubInput),
      ).rejects.toThrow(error);
    });
  });

  describe('updateStudentClub', () => {
    it('should call validateClientType and updateStudentClub successfully', async () => {
      const id = 'test-uuid';
      const clubUpdateInput: any = {
        name: 'Updated Club',
        description: 'Updated Description',
        institution_id: 'inst-123',
      };
      const result = { id, ...clubUpdateInput };
      const mockFiles = {
        club_logo_image: [],
        club_banner_image: [],
      };

      (service.updateStudentClub as jest.Mock).mockResolvedValue(result);

      const response = await controller.updateStudentClub(
        id,
        clubUpdateInput,
        mockFiles,
      );

      expect(service.updateStudentClub).toHaveBeenCalledWith(
        id,
        clubUpdateInput,
      );
      expect(response).toEqual(result);
    });

    it('should handle BadRequestException', async () => {
      const id = 'test-uuid';
      const clubUpdateInput: any = { name: 'Updated Club' };
      const mockFiles = {
        club_logo_image: [],
        club_banner_image: [],
      };
      const error = new BadRequestException(`Club with ID ${id} not found`);

      (service.updateStudentClub as jest.Mock).mockRejectedValue(error);

      try {
        await controller.updateStudentClub(id, clubUpdateInput, mockFiles);
        // If we reach here, the test should fail
        expect(true).toBe(false);
      } catch (e) {
        expect(e).toBe(error);
      }
    });

    it('should upload files if provided', async () => {
      const id = 'test-uuid';
      const clubUpdateInput: any = { name: 'Updated Club' };
      const result = { id, ...clubUpdateInput };
      const mockLogoFile = {
        fieldname: 'club_logo_image',
        originalname: 'logo.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        buffer: Buffer.from('test'),
        size: 4,
        stream: null as any,
        destination: '',
        filename: '',
        path: '',
      };
      const mockBannerFile = {
        fieldname: 'club_banner_image',
        originalname: 'banner.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        buffer: Buffer.from('test'),
        size: 4,
        stream: null as any,
        destination: '',
        filename: '',
        path: '',
      };
      const mockFiles = {
        club_logo_image: [mockLogoFile as Express.Multer.File],
        club_banner_image: [mockBannerFile as Express.Multer.File],
      };

      (uploadService.uploadFileToS3 as jest.Mock).mockResolvedValueOnce({
        imageUrl: 'https://example.com/logo.jpg',
      });
      (uploadService.uploadFileToS3 as jest.Mock).mockResolvedValueOnce({
        imageUrl: 'https://example.com/banner.jpg',
      });
      (service.updateStudentClub as jest.Mock).mockResolvedValue(result);

      await controller.updateStudentClub(id, clubUpdateInput, mockFiles);

      expect(uploadService.uploadFileToS3).toHaveBeenCalledTimes(2);
      expect(uploadService.uploadFileToS3).toHaveBeenCalledWith(mockLogoFile);
      expect(uploadService.uploadFileToS3).toHaveBeenCalledWith(mockBannerFile);
      expect(clubUpdateInput.club_logo_url).toBe(
        'https://example.com/logo.jpg',
      );
      expect(clubUpdateInput.club_banner_url).toBe(
        'https://example.com/banner.jpg',
      );
    });

    it('should handle upload service errors', async () => {
      const id = 'test-uuid';
      const clubUpdateInput: any = { name: 'Updated Club' };
      const mockLogoFile = {
        fieldname: 'club_logo_image',
        originalname: 'logo.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        buffer: Buffer.from('test'),
        size: 4,
        stream: null as any,
        destination: '',
        filename: '',
        path: '',
      };
      const mockFiles = {
        club_logo_image: [mockLogoFile as Express.Multer.File],
        club_banner_image: [],
      };
      const uploadError = new Error('Upload failed');

      (uploadService.uploadFileToS3 as jest.Mock).mockRejectedValue(
        uploadError,
      );

      await expect(
        controller.updateStudentClub(id, clubUpdateInput, mockFiles),
      ).rejects.toThrow(uploadError);

      expect(uploadService.uploadFileToS3).toHaveBeenCalledWith(mockLogoFile);
    });

    it('should handle only logo file upload', async () => {
      const id = 'test-uuid';
      const clubUpdateInput: any = { name: 'Updated Club' };
      const result = { id, ...clubUpdateInput };
      const mockLogoFile = {
        fieldname: 'club_logo_image',
        originalname: 'logo.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        buffer: Buffer.from('test'),
        size: 4,
        stream: null as any,
        destination: '',
        filename: '',
        path: '',
      };
      const mockFiles = {
        club_logo_image: [mockLogoFile as Express.Multer.File],
        club_banner_image: [],
      };

      (uploadService.uploadFileToS3 as jest.Mock).mockResolvedValue({
        imageUrl: 'https://example.com/logo.jpg',
      });
      (service.updateStudentClub as jest.Mock).mockResolvedValue(result);

      await controller.updateStudentClub(id, clubUpdateInput, mockFiles);

      expect(uploadService.uploadFileToS3).toHaveBeenCalledTimes(1);
      expect(uploadService.uploadFileToS3).toHaveBeenCalledWith(mockLogoFile);
      expect(clubUpdateInput.club_logo_url).toBe(
        'https://example.com/logo.jpg',
      );
      expect(clubUpdateInput.club_banner_url).toBeUndefined();
    });

    it('should handle only banner file upload', async () => {
      const id = 'test-uuid';
      const clubUpdateInput: any = { name: 'Updated Club' };
      const result = { id, ...clubUpdateInput };
      const mockBannerFile = {
        fieldname: 'club_banner_image',
        originalname: 'banner.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        buffer: Buffer.from('test'),
        size: 4,
        stream: null as any,
        destination: '',
        filename: '',
        path: '',
      };
      const mockFiles = {
        club_logo_image: [],
        club_banner_image: [mockBannerFile as Express.Multer.File],
      };

      (uploadService.uploadFileToS3 as jest.Mock).mockResolvedValue({
        imageUrl: 'https://example.com/banner.jpg',
      });
      (service.updateStudentClub as jest.Mock).mockResolvedValue(result);

      await controller.updateStudentClub(id, clubUpdateInput, mockFiles);

      expect(uploadService.uploadFileToS3).toHaveBeenCalledTimes(1);
      expect(uploadService.uploadFileToS3).toHaveBeenCalledWith(mockBannerFile);
      expect(clubUpdateInput.club_banner_url).toBe(
        'https://example.com/banner.jpg',
      );
      expect(clubUpdateInput.club_logo_url).toBeUndefined();
    });
  });

  describe('removeStudentClub', () => {
    it('should call validateClientType and removeStudentClub successfully', async () => {
      const id = 'test-uuid';

      (service.removeStudentClub as jest.Mock).mockResolvedValue(undefined);

      await controller.removeStudentClub(id);

      expect(service.removeStudentClub).toHaveBeenCalledWith(id);
    });

    it('should handle BadRequestException', async () => {
      const id = 'test-uuid';
      const error = new BadRequestException(`Club with ID ${id} not found`);

      (service.removeStudentClub as jest.Mock).mockRejectedValue(error);

      try {
        await controller.removeStudentClub(id);
        // If we reach here, the test should fail
        expect(true).toBe(false);
      } catch (e) {
        expect(e).toBe(error);
      }
    });
  });

  describe('getStudentClub', () => {
    it('should call validateClientType and getClubById successfully', async () => {
      const id = 'test-uuid';
      const result = {
        id,
        name: 'Test Club',
        description: 'Test Description',
        institution_id: 'inst-123',
        country_id: 'country-123',
      };

      (service.getClubById as jest.Mock).mockResolvedValue(result);

      const response = await controller.getStudentClub(id);

      expect(service.getClubById).toHaveBeenCalledWith(id);
      expect(response).toEqual(result);
    });

    it('should handle NotFoundException', async () => {
      const id = 'test-uuid';
      const error = new NotFoundException(`Club with ID ${id} not found`);

      (service.getClubById as jest.Mock).mockRejectedValue(error);

      try {
        await controller.getStudentClub(id);
        expect(true).toBe(false);
      } catch (e) {
        expect(e).toBe(error);
      }
    });

    it('should add isMember flag when user with student profile is provided', async () => {
      const id = 'test-uuid';
      const club = {
        id,
        name: 'Test Club',
        description: 'Test Description',
        institution_id: 'inst-123',
        country_id: 'country-123',
      };
      const studentClubs = [
        {
          club: { id: 'test-uuid' },
          membership: { is_active: true },
        },
      ];

      (service.getClubById as jest.Mock).mockResolvedValue(club);
      (service.getStudentClubByStudentId as jest.Mock).mockResolvedValue(
        studentClubs,
      );

      const response = await controller.getStudentClub(
        id,
        mockUserWithStudentProfile,
      );

      expect(service.getClubById).toHaveBeenCalledWith(id);
      expect(service.getStudentClubByStudentId).toHaveBeenCalledWith(
        'student-profile-123',
      );
      expect((response as any).isMember).toBe(true);
    });

    it('should set isMember to false when user is not a member', async () => {
      const id = 'test-uuid';
      const club = {
        id,
        name: 'Test Club',
        description: 'Test Description',
        institution_id: 'inst-123',
        country_id: 'country-123',
      };
      const studentClubs = [
        {
          club: { id: 'different-club-id' },
          membership: { is_active: true },
        },
      ];

      (service.getClubById as jest.Mock).mockResolvedValue(club);
      (service.getStudentClubByStudentId as jest.Mock).mockResolvedValue(
        studentClubs,
      );

      const response = await controller.getStudentClub(
        id,
        mockUserWithStudentProfile,
      );

      expect((response as any).isMember).toBe(false);
    });

    it('should set isMember to false when user has no student profile', async () => {
      const id = 'test-uuid';
      const club = {
        id,
        name: 'Test Club',
        description: 'Test Description',
        institution_id: 'inst-123',
        country_id: 'country-123',
      };

      (service.getClubById as jest.Mock).mockResolvedValue(club);

      const response = await controller.getStudentClub(id, mockUser);

      expect(service.getClubById).toHaveBeenCalledWith(id);
      expect(service.getStudentClubByStudentId).not.toHaveBeenCalled();
      expect((response as any).isMember).toBe(false);
    });

    it('should handle inactive membership correctly', async () => {
      const id = 'test-uuid';
      const club = {
        id,
        name: 'Test Club',
        description: 'Test Description',
        institution_id: 'inst-123',
        country_id: 'country-123',
      };
      const studentClubs = [
        {
          club: { id: 'test-uuid' },
          membership: { is_active: false },
        },
      ];

      (service.getClubById as jest.Mock).mockResolvedValue(club);
      (service.getStudentClubByStudentId as jest.Mock).mockResolvedValue(
        studentClubs,
      );

      const response = await controller.getStudentClub(
        id,
        mockUserWithStudentProfile,
      );

      expect((response as any).isMember).toBe(false);
    });

    it('should handle empty student clubs array', async () => {
      const id = 'test-uuid';
      const club = {
        id,
        name: 'Test Club',
        description: 'Test Description',
        institution_id: 'inst-123',
        country_id: 'country-123',
      };

      (service.getClubById as jest.Mock).mockResolvedValue(club);
      (service.getStudentClubByStudentId as jest.Mock).mockResolvedValue([]);

      const response = await controller.getStudentClub(
        id,
        mockUserWithStudentProfile,
      );

      expect((response as any).isMember).toBe(false);
    });

    it('should handle null student clubs response', async () => {
      const id = 'test-uuid';
      const club = {
        id,
        name: 'Test Club',
        description: 'Test Description',
        institution_id: 'inst-123',
        country_id: 'country-123',
      };

      (service.getClubById as jest.Mock).mockResolvedValue(club);
      (service.getStudentClubByStudentId as jest.Mock).mockResolvedValue(null);

      const response = await controller.getStudentClub(
        id,
        mockUserWithStudentProfile,
      );

      // When studentClubs is null, the logical AND operation results in null/false
      expect((response as any).isMember).toBeFalsy();
    });

    it('should handle student clubs with missing club or membership data', async () => {
      const id = 'test-uuid';
      const club = {
        id,
        name: 'Test Club',
        description: 'Test Description',
        institution_id: 'inst-123',
        country_id: 'country-123',
      };
      const studentClubs = [
        {
          club: null,
          membership: { is_active: true },
        },
        {
          club: { id: 'test-uuid' },
          membership: null,
        },
      ];

      (service.getClubById as jest.Mock).mockResolvedValue(club);
      (service.getStudentClubByStudentId as jest.Mock).mockResolvedValue(
        studentClubs,
      );

      const response = await controller.getStudentClub(
        id,
        mockUserWithStudentProfile,
      );

      expect((response as any).isMember).toBe(false);
    });
  });

  describe('getAllClubs', () => {
    // Create a mock request object to be reused
    const createMockRequest = () =>
      ({
        headers: { 'x-client-type': 'web' },
        cookies: {},
        signedCookies: {},
        get: jest.fn(),
        header: jest.fn(),
        accepts: jest.fn(),
        acceptsEncoding: jest.fn(),
        acceptsLanguages: jest.fn(),
        range: jest.fn(),
        param: jest.fn(),
        is: jest.fn(),
        protocol: 'http',
        secure: false,
        ip: '127.0.0.1',
        ips: [],
        subdomains: [],
        path: '',
        hostname: '',
        host: '',
        fresh: false,
        stale: true,
        xhr: false,
        body: {},
        params: {},
        query: {},
        route: {},
        originalUrl: '',
        baseUrl: '',
        url: '',
        method: 'GET',
      }) as unknown as Request;

    it('should fetch all clubs successfully', async () => {
      const result = {
        data: [
          {
            id: '1',
            name: 'Test Club',
            description: 'Test Description',
            institution_id: 'inst-123',
            country_id: 'country-123',
          },
        ],
        total: 1,
      };
      const queryParams = {
        page: 1,
        limit: 10,
        search: '',
        sort: 'name',
        order: 'asc' as 'asc' | 'desc',
        all: true,
      };

      (service.getAllClubs as jest.Mock).mockResolvedValue(result);
      (service.addIsMemberFlagToClubs as jest.Mock).mockResolvedValue(result);
      const mockRequest = createMockRequest();

      const response = await controller.getAllClubs(
        mockUser,
        queryParams,
        mockRequest,
      );

      expect(service.getAllClubs).toHaveBeenCalledWith(
        mockUser,
        queryParams,
        mockRequest,
      );
      expect(service.addIsMemberFlagToClubs).toHaveBeenCalledWith(
        result,
        mockUser,
      );
      expect(response).toEqual(result);
    });

    it('should handle errors', async () => {
      const errorMessage = 'Error fetching all clubs';
      const queryParams = {
        page: 1,
        limit: 10,
        search: '',
        sort: 'name',
        order: 'asc' as 'asc' | 'desc',
        all: true,
      };
      const error = new Error(errorMessage);

      (service.getAllClubs as jest.Mock).mockRejectedValue(error);
      const mockRequest = createMockRequest();

      try {
        await controller.getAllClubs(mockUser, queryParams, mockRequest);
        // If we reach here, the test should fail
        expect(true).toBe(false);
      } catch (e) {
        expect(e).toBe(error);
      }
    });
  });

  describe('removeStudentFromClub', () => {
    it('should remove a student from a club successfully', async () => {
      const clubId = 'club-123';
      const studentId = 'student-123';
      const result = { success: true };

      (service.removeStudentFromClub as jest.Mock).mockResolvedValue(result);

      const response = await controller.removeStudentFromClub(
        clubId,
        studentId,
      );

      expect(service.removeStudentFromClub).toHaveBeenCalledWith(
        clubId,
        studentId,
      );
      expect(response).toEqual(result);
    });

    it('should handle errors from service', async () => {
      const clubId = 'club-123';
      const studentId = 'student-123';
      const error = new Error('Error removing student from club');

      (service.removeStudentFromClub as jest.Mock).mockRejectedValue(error);

      try {
        await controller.removeStudentFromClub(clubId, studentId);
        // If we reach here, the test should fail
        expect(true).toBe(false);
      } catch (e) {
        expect(e).toBe(error);
      }
    });
  });

  describe('getActiveClubs', () => {
    it('should get active clubs successfully', async () => {
      const queryParams = {
        page: 1,
        limit: 10,
        search: '',
        sort: 'name',
        order: 'asc' as 'asc' | 'desc',
        all: true,
      };
      const result = {
        data: [{ id: '1', name: 'Active Club', is_active: true }],
        total: 1,
      };

      (service.getActiveClubs as jest.Mock).mockResolvedValue(result);
      (service.addIsMemberFlagToClubs as jest.Mock).mockResolvedValue(result);

      const response = await controller.getActiveClubs(queryParams, mockUser);

      expect(service.getActiveClubs).toHaveBeenCalledWith(queryParams);
      expect(service.addIsMemberFlagToClubs).toHaveBeenCalledWith(
        result,
        mockUser,
      );
      expect(response).toEqual(result);
    });

    it('should handle errors', async () => {
      const queryParams = {
        page: 1,
        limit: 10,
        search: '',
        sort: 'name',
        order: 'asc' as 'asc' | 'desc',
        all: true,
      };
      const error = new Error('Failed to get active clubs');

      (service.getActiveClubs as jest.Mock).mockRejectedValue(error);

      await expect(
        controller.getActiveClubs(queryParams, mockUser),
      ).rejects.toThrow(error);
    });

    it('should work without user parameter', async () => {
      const queryParams = {
        page: 1,
        limit: 10,
        search: '',
        sort: 'name',
        order: 'asc' as 'asc' | 'desc',
        all: true,
      };
      const result = {
        data: [{ id: '1', name: 'Active Club', is_active: true }],
        total: 1,
      };

      (service.getActiveClubs as jest.Mock).mockResolvedValue(result);
      (service.addIsMemberFlagToClubs as jest.Mock).mockResolvedValue(result);

      const response = await controller.getActiveClubs(queryParams);

      expect(service.getActiveClubs).toHaveBeenCalledWith(queryParams);
      expect(service.addIsMemberFlagToClubs).toHaveBeenCalledWith(
        result,
        undefined,
      );
      expect(response).toEqual(result);
    });
  });

  describe('deactivateClub', () => {
    it('should get inactive clubs successfully', async () => {
      const result = [{ id: '1', name: 'Inactive Club', is_active: false }];

      (service.getInactiveClubs as jest.Mock).mockResolvedValue(result);

      const response = await controller.deactivateClub();

      expect(service.getInactiveClubs).toHaveBeenCalled();
      expect(response).toEqual(result);
    });

    it('should handle errors', async () => {
      const error = new Error('Failed to get inactive clubs');

      (service.getInactiveClubs as jest.Mock).mockRejectedValue(error);

      await expect(controller.deactivateClub()).rejects.toThrow(error);
    });
  });

  describe('getClubMembers', () => {
    it('should get club members successfully', async () => {
      const clubId = 'club-123';
      const queryParams = {
        page: 1,
        limit: 10,
        search: '',
        sort: 'id',
        order: 'asc' as 'asc' | 'desc',
        all: true,
      };
      const result = {
        data: [{ id: 'student-1', name: 'Student 1' }],
        total: 1,
      };

      (service.getClubMembersByClubId as jest.Mock).mockResolvedValue(result);

      const response = await controller.getClubMembers(clubId, queryParams);

      expect(service.getClubMembersByClubId).toHaveBeenCalledWith(
        clubId,
        queryParams,
      );
      expect(response).toEqual(result);
    });

    it('should handle errors', async () => {
      const clubId = 'club-123';
      const queryParams = {
        page: 1,
        limit: 10,
        search: '',
        sort: 'id',
        order: 'asc' as 'asc' | 'desc',
        all: true,
      };
      const error = new Error('Failed to get club members');

      (service.getClubMembersByClubId as jest.Mock).mockRejectedValue(error);

      await expect(
        controller.getClubMembers(clubId, queryParams),
      ).rejects.toThrow(error);
    });

    it('should handle query with role filter', async () => {
      const clubId = 'club-123';
      const queryParams = {
        page: 1,
        limit: 10,
        search: '',
        sort: 'id',
        order: 'asc' as 'asc' | 'desc',
        all: true,
        role: 'member' as 'member' | 'club_admin',
      };
      const result = {
        data: [{ id: 'student-1', name: 'Student 1', role: 'member' }],
        total: 1,
      };

      (service.getClubMembersByClubId as jest.Mock).mockResolvedValue(result);

      const response = await controller.getClubMembers(clubId, queryParams);

      expect(service.getClubMembersByClubId).toHaveBeenCalledWith(
        clubId,
        queryParams,
      );
      expect(response).toEqual(result);
    });
  });

  describe('addStudentToClub', () => {
    it('should add student to club successfully', async () => {
      const clubId = 'club-123';
      const data = { student_id: 'student-123' };

      (service.addStudentToClub as jest.Mock).mockResolvedValue(undefined);

      const response = await controller.addStudentToClub(clubId, data);

      expect(service.addStudentToClub).toHaveBeenCalledWith(
        clubId,
        data.student_id,
      );
      expect(response.message).toBe('Student added to club successfully');
    });

    it('should handle errors', async () => {
      const clubId = 'club-123';
      const data = { student_id: 'student-123' };
      const error = new Error('Failed to add student to club');

      (service.addStudentToClub as jest.Mock).mockRejectedValue(error);

      await expect(controller.addStudentToClub(clubId, data)).rejects.toThrow(
        error,
      );
    });
  });

  describe('disableClub', () => {
    it('should update club status successfully', async () => {
      const clubId = 'club-123';
      const isActive = false;
      const result = { id: clubId, is_active: isActive };

      (service.setClubStatus as jest.Mock).mockResolvedValue(result);

      const response = await controller.disableClub(clubId, isActive);

      expect(service.setClubStatus).toHaveBeenCalledWith(clubId, isActive);
      expect(response).toEqual(result);
    });

    it('should handle errors', async () => {
      const clubId = 'club-123';
      const isActive = false;
      const error = new Error('Failed to update club status');

      (service.setClubStatus as jest.Mock).mockRejectedValue(error);

      await expect(controller.disableClub(clubId, isActive)).rejects.toThrow(
        error,
      );
    });
  });

  describe('getStudentClubByStudentId', () => {
    it('should get student clubs by student ID successfully', async () => {
      const studentId = 'student-123';
      const result = [
        {
          club: { id: 'club-1', name: 'Club 1' },
          membership: { role: 'member' },
        },
      ];

      (service.getStudentClubByStudentId as jest.Mock).mockResolvedValue(
        result,
      );

      const response = await controller.getStudentClubByStudentId(studentId);

      expect(service.getStudentClubByStudentId).toHaveBeenCalledWith(studentId);
      expect(response).toEqual(result);
    });

    it('should handle errors', async () => {
      const studentId = 'student-123';
      const error = new Error('Failed to get student clubs');

      (service.getStudentClubByStudentId as jest.Mock).mockRejectedValue(error);

      await expect(
        controller.getStudentClubByStudentId(studentId),
      ).rejects.toThrow(error);
    });
  });

  describe('getClubByInstitutionId', () => {
    it('should get clubs by institution ID successfully', async () => {
      const institutionId = 'inst-123';
      const result = { data: [{ id: 'club-1', name: 'Club 1' }], total: 1 };

      (service.getClubMembersByInstitutionId as jest.Mock).mockResolvedValue(
        result,
      );
      (service.addIsMemberFlagToClubs as jest.Mock).mockResolvedValue(result);

      const response = await controller.getClubByInstitutionId(
        institutionId,
        mockUser,
      );

      expect(service.getClubMembersByInstitutionId).toHaveBeenCalledWith(
        institutionId,
      );
      expect(service.addIsMemberFlagToClubs).toHaveBeenCalledWith(
        result,
        mockUser,
      );
      expect(response).toEqual(result);
    });

    it('should handle errors', async () => {
      const institutionId = 'inst-123';
      const error = new Error('Failed to get clubs by institution');

      (service.getClubMembersByInstitutionId as jest.Mock).mockRejectedValue(
        error,
      );

      await expect(
        controller.getClubByInstitutionId(institutionId, mockUser),
      ).rejects.toThrow(error);
    });
  });

  describe('updateStudentMembershipRole', () => {
    it('should update student membership role successfully', async () => {
      const clubId = 'club-123';
      const studentId = 'student-123';
      const data = { role: 'club_admin' as 'club_admin' | 'member' };
      const result = { success: true };

      (service.updateStudentMembershipRole as jest.Mock).mockResolvedValue(
        result,
      );

      const response = await controller.updateStudentMembershipRole(
        clubId,
        studentId,
        data,
      );

      expect(service.updateStudentMembershipRole).toHaveBeenCalledWith(
        clubId,
        studentId,
        data.role,
      );
      expect(response).toEqual(result);
    });

    it('should handle errors', async () => {
      const clubId = 'club-123';
      const studentId = 'student-123';
      const data = { role: 'member' as 'club_admin' | 'member' };
      const error = new Error('Failed to update membership role');

      (service.updateStudentMembershipRole as jest.Mock).mockRejectedValue(
        error,
      );

      await expect(
        controller.updateStudentMembershipRole(clubId, studentId, data),
      ).rejects.toThrow(error);
    });

    it('should handle validation errors for invalid role', async () => {
      const clubId = 'club-123';
      const studentId = 'student-123';
      const data = { role: 'member' as 'club_admin' | 'member' };
      const error = new BadRequestException('Invalid role specified');

      (service.updateStudentMembershipRole as jest.Mock).mockRejectedValue(
        error,
      );

      await expect(
        controller.updateStudentMembershipRole(clubId, studentId, data),
      ).rejects.toThrow(error);
    });
  });

  describe('leaveClub', () => {
    it('should allow authenticated student to leave a club successfully', async () => {
      const clubId = 'club-123';
      const result = { success: true };

      (service.removeStudentFromClub as jest.Mock).mockResolvedValue(result);

      const response = await controller.leaveClub(
        clubId,
        mockAuthenticatedStudent,
      );

      expect(service.removeStudentFromClub).toHaveBeenCalledWith(
        clubId,
        'student-profile-123',
      );
      expect(response).toEqual(result);
    });

    it('should throw BadRequestException when student profile is not found', async () => {
      const clubId = 'club-123';
      const userWithoutProfile = {
        ...mockUser,
        student_profile: null,
      } as any;

      await expect(
        controller.leaveClub(clubId, userWithoutProfile),
      ).rejects.toThrow(new BadRequestException('Student profile not found'));

      expect(service.removeStudentFromClub).not.toHaveBeenCalled();
    });

    it('should handle errors from service when leaving club', async () => {
      const clubId = 'club-123';
      const error = new Error('Failed to remove student from club');

      (service.removeStudentFromClub as jest.Mock).mockRejectedValue(error);

      await expect(
        controller.leaveClub(clubId, mockAuthenticatedStudent),
      ).rejects.toThrow(error);

      expect(service.removeStudentFromClub).toHaveBeenCalledWith(
        clubId,
        'student-profile-123',
      );
    });

    it('should handle NotFoundException when club does not exist', async () => {
      const clubId = 'non-existent-club';
      const error = new NotFoundException('Club not found');

      (service.removeStudentFromClub as jest.Mock).mockRejectedValue(error);

      await expect(
        controller.leaveClub(clubId, mockAuthenticatedStudent),
      ).rejects.toThrow(error);
    });

    it('should handle BadRequestException when student is not a member', async () => {
      const clubId = 'club-123';
      const error = new BadRequestException(
        'Student is not a member of this club',
      );

      (service.removeStudentFromClub as jest.Mock).mockRejectedValue(error);

      await expect(
        controller.leaveClub(clubId, mockAuthenticatedStudent),
      ).rejects.toThrow(error);
    });
  });
});
