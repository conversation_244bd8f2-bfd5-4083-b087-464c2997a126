import * as Handlebars from 'handlebars';
import { registerHandlebarsHelpers } from './handlebars-helpers';

describe('Handlebars Helpers', () => {
  beforeAll(() => {
    registerHandlebarsHelpers();
  });

  describe('contains helper', () => {
    it('should return true when string contains substring (case insensitive)', () => {
      const template = Handlebars.compile(
        '{{#if (contains text "constraint")}}Found{{else}}Not found{{/if}}',
      );

      const result1 = template({ text: 'violates not-null constraint' });
      expect(result1).toBe('Found');

      const result2 = template({ text: 'VIOLATES NOT-NULL CONSTRAINT' });
      expect(result2).toBe('Found');

      const result3 = template({ text: 'foreign key constraint violation' });
      expect(result3).toBe('Found');
    });

    it('should return false when string does not contain substring', () => {
      const template = Handlebars.compile(
        '{{#if (contains text "constraint")}}Found{{else}}Not found{{/if}}',
      );

      const result = template({ text: 'some other error message' });
      expect(result).toBe('Not found');
    });

    it('should handle null/undefined values gracefully', () => {
      const template = Handlebars.compile(
        '{{#if (contains text "constraint")}}Found{{else}}Not found{{/if}}',
      );

      const result1 = template({ text: null });
      expect(result1).toBe('Not found');

      const result2 = template({ text: undefined });
      expect(result2).toBe('Not found');

      const result3 = template({});
      expect(result3).toBe('Not found');
    });

    it('should work with bulk deletion error messages', () => {
      const template = Handlebars.compile(`
        {{#if (contains reason "violates not-null constraint")}}
          Database Constraint Error
        {{else if (contains reason "foreign key constraint")}}
          Foreign Key Error
        {{else}}
          Other Error
        {{/if}}
      `);

      const result1 = template({
        reason:
          'null value in column "created_by" of relation "scheduled_notifications" violates not-null constraint',
      });
      expect(result1.trim()).toBe('Database Constraint Error');

      const result2 = template({
        reason: 'violates foreign key constraint "fk_posts_created_by"',
      });
      expect(result2.trim()).toBe('Foreign Key Error');

      const result3 = template({
        reason: 'some other database error',
      });
      expect(result3.trim()).toBe('Other Error');
    });
  });

  describe('other helpers', () => {
    it('should have eq helper working', () => {
      const template = Handlebars.compile(
        '{{#if (eq status "active")}}Active{{else}}Inactive{{/if}}',
      );

      const result1 = template({ status: 'active' });
      expect(result1).toBe('Active');

      const result2 = template({ status: 'inactive' });
      expect(result2).toBe('Inactive');
    });

    it('should have truncate helper working', () => {
      const template = Handlebars.compile('{{truncate text 10}}');

      const result1 = template({
        text: 'This is a very long text that should be truncated',
      });
      expect(result1).toBe('This is a ...');

      const result2 = template({ text: 'Short' });
      expect(result2).toBe('Short');
    });
  });
});
