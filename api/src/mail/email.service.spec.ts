import { Test, TestingModule } from '@nestjs/testing';
import { EmailService } from './email.service';
import { MailerService } from '@nestjs-modules/mailer';
import { EnvConfig } from '@app/shared/dto/env-config.dto';
import { InternalServerErrorException, Logger } from '@nestjs/common';

describe('EmailService', () => {
  let emailService: EmailService;

  const mockMailerService = {
    sendMail: jest.fn(),
  };

  // Using 'as EnvConfig' to bypass TypeScript type checking for the mock
  const mockEnvConfig = {
    APP_NAME: 'TestApp',
    FRONTEND_URL: 'https://localhost:3000',
  } as EnvConfig;

  // Mock the Logger to prevent error logs in test output
  const originalLoggerError = Logger.prototype.error;
  beforeAll(() => {
    // Replace the error method with a no-op function
    Logger.prototype.error = jest.fn();
  });

  afterAll(() => {
    // Restore the original error method
    Logger.prototype.error = originalLoggerError;
  });

  beforeEach(async () => {
    jest.clearAllMocks(); // Reset all mocks before each test

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailService,
        { provide: MailerService, useValue: mockMailerService },
        { provide: EnvConfig, useValue: mockEnvConfig },
      ],
    }).compile();

    emailService = module.get<EmailService>(EmailService);
  });

  it('should be defined', () => {
    expect(emailService).toBeDefined();
  });

  describe('sendOtp', () => {
    it('should send OTP email', async () => {
      mockMailerService.sendMail.mockResolvedValueOnce(undefined);

      await expect(
        emailService.sendOtp('<EMAIL>', '123456'),
      ).resolves.not.toThrow();
      expect(mockMailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'OTP Verification',
        template: 'otp-code',
        context: {
          otpCode: '123456',
          appName: 'TestApp',
          appUrl: 'https://localhost:3000',
        },
      });
    });

    it('should throw InternalServerErrorException on failure', async () => {
      mockMailerService.sendMail.mockRejectedValueOnce(
        new Error('SendMail Error'),
      );

      await expect(
        emailService.sendOtp('<EMAIL>', '123456'),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('newOrganisation', () => {
    it('should send new organisation email', async () => {
      mockMailerService.sendMail.mockResolvedValueOnce(undefined);

      await expect(
        emailService.newOrganisation(
          '<EMAIL>',
          'TestOrg',
          'https://login-link.com',
        ),
      ).resolves.not.toThrow();
      expect(mockMailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'New Organisation',
        template: 'new-organisation',
        context: {
          organizationName: 'TestOrg',
          signInLink: 'https://login-link.com',
          appName: 'TestApp',
          appUrl: 'https://localhost:3000',
        },
      });
    });

    it('should throw InternalServerErrorException on failure', async () => {
      mockMailerService.sendMail.mockRejectedValueOnce(
        new Error('SendMail Error'),
      );

      await expect(
        emailService.newOrganisation(
          '<EMAIL>',
          'TestOrg',
          'https://login-link.com',
        ),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('sendEmailSignInLink', () => {
    it('should send sign-in link email', async () => {
      mockMailerService.sendMail.mockResolvedValueOnce(undefined);

      await expect(
        emailService.sendEmailSignInLink(
          '<EMAIL>',
          'https://signin-link.com',
        ),
      ).resolves.not.toThrow();
      expect(mockMailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Sign In Link',
        template: 'email-signin',
        context: {
          signInLink: 'https://signin-link.com',
          appName: 'TestApp',
          appUrl: 'https://localhost:3000',
        },
      });
    });

    it('should throw InternalServerErrorException on failure', async () => {
      mockMailerService.sendMail.mockRejectedValueOnce(
        new Error('SendMail Error'),
      );

      await expect(
        emailService.sendEmailSignInLink(
          '<EMAIL>',
          'https://signin-link.com',
        ),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('sendStudentAdminPromotion', () => {
    it('should send student admin promotion email', async () => {
      mockMailerService.sendMail.mockResolvedValueOnce(undefined);

      await expect(
        emailService.sendStudentAdminPromotion({
          email: '<EMAIL>',
          clubName: 'TestClub',
          studentName: 'John Doe',
        }),
      ).resolves.not.toThrow();
      expect(mockMailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Club Role Change Notification',
        template: 'add-student-admin',
        context: {
          clubName: 'TestClub',
          studentName: 'John Doe',
          appName: 'TestApp',
          appUrl: 'https://localhost:3000',
          signInLink: 'https://localhost:3000',
        },
      });
    });

    it('should throw InternalServerErrorException on failure', async () => {
      mockMailerService.sendMail.mockRejectedValueOnce(
        new Error('SendMail Error'),
      );

      await expect(
        emailService.sendStudentAdminPromotion({
          email: '<EMAIL>',
          clubName: 'TestClub',
          studentName: 'John Doe',
        }),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('waitingListNotification', () => {
    it('should send waiting list notification email', async () => {
      mockMailerService.sendMail.mockResolvedValueOnce(undefined);

      await expect(
        emailService.waitingListNotification({ email: '<EMAIL>' }),
      ).resolves.not.toThrow();
      expect(mockMailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Account Activation Notification',
        template: 'waiting-list',
        context: {
          appName: 'TestApp',
          appUrl: 'https://localhost:3000',
          message: 'Go to the App and logon again',
        },
      });
    });

    it('should throw InternalServerErrorException on failure', async () => {
      mockMailerService.sendMail.mockRejectedValueOnce(
        new Error('SendMail Error'),
      );

      await expect(
        emailService.waitingListNotification({ email: '<EMAIL>' }),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('sendCustomEmail', () => {
    it('should send bulk deletion report email with failure details', async () => {
      mockMailerService.sendMail.mockResolvedValueOnce(undefined);

      const bulkDeletionContext = {
        totalProcessed: 5,
        successCount: 3,
        failureCount: 2,
        failures: [
          {
            userId: 'user-1',
            email: '<EMAIL>',
            reason:
              'null value in column "created_by" of relation "scheduled_notifications" violates not-null constraint',
          },
          {
            userId: 'user-2',
            email: '<EMAIL>',
            reason: 'violates foreign key constraint "fk_posts_created_by"',
          },
        ],
        completedAt: '2024-01-01 12:00:00',
        adminName: 'Admin User',
        adminEmail: '<EMAIL>',
        adminRole: 'super_admin',
        duration: '2.5 seconds',
      };

      await expect(
        emailService.sendCustomEmail({
          email: '<EMAIL>',
          subject: 'Bulk User Deletion Report - Operation Complete',
          template: 'bulk-deletion-report',
          context: bulkDeletionContext,
          critical: true,
        }),
      ).resolves.not.toThrow();

      expect(mockMailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Bulk User Deletion Report - Operation Complete',
        template: 'bulk-deletion-report',
        context: {
          ...bulkDeletionContext,
          appName: 'TestApp',
          appUrl: 'https://localhost:3000',
          userName: 'Reach User',
        },
      });
    });
  });
});
