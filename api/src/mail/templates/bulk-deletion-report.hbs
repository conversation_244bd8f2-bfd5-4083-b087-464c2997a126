{{#> base subject="Bulk User Deletion Report - Operation Complete" customDisclaimer="This is an automated message from Reach. Please do not reply to this email."}}

<!-- Main Content Container -->
<tr>
  <td style="padding: 0; background-color: #ffffff;">

    <!-- Modern Header Section -->
    <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0">
      <tr>
        <td style="background-color: #f8f9fa; padding: 32px 24px; text-align: center; border-bottom: 1px solid #e9ecef;">
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0">
            <tr>
              <td style="text-align: center;">
                <!-- Professional Delete Icon -->
                <div style="background-color: #ffffff; border-radius: 16px; display: inline-block; padding: 16px; margin-bottom: 16px; box-shadow: 0 2px 8px rgba(0,0,0,0.08); border: 1px solid #e9ecef;">
                  <svg width="32" height="32" viewBox="0 0 24 24" fill="none" style="display: block;">
                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z" fill="#6c757d"/>
                  </svg>
                </div>
                <!-- Typography Hierarchy -->
                <h1 style="color: #212529; margin: 0 0 8px 0; font-size: 28px; font-weight: 600; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; letter-spacing: -0.5px;">
                  Bulk User Deletion Report
                </h1>
                <div style="background-color: #d1ecf1; color: #0c5460; padding: 8px 16px; border-radius: 24px; display: inline-block; margin-bottom: 12px; font-size: 14px; font-weight: 500; border: 1px solid #bee5eb;">
                  Operation Completed Successfully
                </div>
                <p style="color: #6c757d; margin: 0; font-size: 14px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                  {{completedAt}}
                </p>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>

    <!-- Content Wrapper -->
    <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0">
      <tr>
        <td style="padding: 32px 24px;">

          <!-- Modern Greeting Card -->
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="margin-bottom: 32px;">
            <tr>
              <td style="background-color: #ffffff; padding: 24px; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e9ecef;">
                <p style="color: #212529; margin: 0 0 8px 0; font-size: 18px; line-height: 1.4; font-weight: 500; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                  Hello {{adminName}},
                </p>
                <p style="color: #6c757d; margin: 0; font-size: 16px; line-height: 1.5; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                  This report provides a detailed analysis of the bulk user deletion operation, including processing statistics, success metrics, error details, and system impact assessment.
                </p>
              </td>
            </tr>
          </table>

          <!-- Modern Statistics Dashboard -->
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="margin-bottom: 32px;">
            <tr>
              <td>
                <h2 style="color: #212529; margin: 0 0 24px 0; font-size: 24px; font-weight: 600; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; letter-spacing: -0.3px;">
                  Operation Summary
                </h2>

                <!-- Statistics Cards Grid -->
                <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0">
                  <tr>
                    <!-- Total Processed Card -->
                    <td style="width: 48%; vertical-align: top; padding-right: 8px;">
                      <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.08); border: 1px solid #e9ecef;">
                        <tr>
                          <td style="padding: 24px; text-align: center;">
                            <div style="background-color: #f8f9fa; color: #495057; border-radius: 12px; width: 56px; height: 56px; line-height: 56px; margin: 0 auto 16px auto; font-size: 24px; font-weight: 700; border: 2px solid #e9ecef;">
                              {{totalProcessed}}
                            </div>
                            <h3 style="color: #495057; margin: 0 0 4px 0; font-size: 16px; font-weight: 600; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">Total Processed</h3>
                            <p style="color: #6c757d; margin: 0; font-size: 14px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">Users in operation</p>
                          </td>
                        </tr>
                      </table>
                    </td>

                    <!-- Duration Card -->
                    <td style="width: 48%; vertical-align: top; padding-left: 8px;">
                      <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.08); border: 1px solid #e9ecef;">
                        <tr>
                          <td style="padding: 24px; text-align: center;">
                            <div style="background-color: #e3f2fd; color: #1976d2; border-radius: 12px; width: 56px; height: 56px; line-height: 56px; margin: 0 auto 16px auto; font-size: 20px; font-weight: 600; border: 2px solid #bbdefb;">
                              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" style="vertical-align: middle;">
                                <path d="M15,1H9V3H15M11,14H13V8H11M19,3H18V1A1,1 0 0,0 17,0H7A1,1 0 0,0 6,1V3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3Z"/>
                              </svg>
                            </div>
                            <h3 style="color: #495057; margin: 0 0 4px 0; font-size: 16px; font-weight: 600; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">Duration</h3>
                            <p style="color: #6c757d; margin: 0; font-size: 14px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">{{duration}}</p>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>

                <!-- Success/Failure Cards Row -->
                <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="margin-top: 16px;">
                  <tr>
                    <!-- Success Card -->
                    <td style="width: 48%; vertical-align: top; padding-right: 8px;">
                      <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.08); border: 1px solid #e9ecef;">
                        <tr>
                          <td style="padding: 24px; text-align: center;">
                            <div style="background-color: #e8f5e8; color: #2e7d32; border-radius: 12px; width: 56px; height: 56px; line-height: 56px; margin: 0 auto 16px auto; font-size: 24px; font-weight: 700; border: 2px solid #c8e6c9;">
                              {{successCount}}
                            </div>
                            <h3 style="color: #2e7d32; margin: 0 0 4px 0; font-size: 16px; font-weight: 600; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" style="vertical-align: middle; margin-right: 4px;">
                                <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
                              </svg>
                              Successful
                            </h3>
                            <p style="color: #6c757d; margin: 0; font-size: 14px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">Users deleted</p>
                          </td>
                        </tr>
                      </table>
                    </td>

                    <!-- Failure Card -->
                    <td style="width: 48%; vertical-align: top; padding-left: 8px;">
                      <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.08); border: 1px solid #e9ecef;">
                        <tr>
                          <td style="padding: 24px; text-align: center;">
                            <div style="background-color: #ffebee; color: #d32f2f; border-radius: 12px; width: 56px; height: 56px; line-height: 56px; margin: 0 auto 16px auto; font-size: 24px; font-weight: 700; border: 2px solid #ffcdd2;">
                              {{failureCount}}
                            </div>
                            <h3 style="color: #d32f2f; margin: 0 0 4px 0; font-size: 16px; font-weight: 600; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" style="vertical-align: middle; margin-right: 4px;">
                                <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                              </svg>
                              Failed
                            </h3>
                            <p style="color: #6c757d; margin: 0; font-size: 14px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">Deletion errors</p>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>

                <!-- Question Cleanup Statistics (if any questions were handled) -->
                {{#if questionsHandled}}
                <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="margin-top: 16px;">
                  <tr>
                    <td>
                      <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.08); border: 1px solid #e9ecef;">
                        <tr>
                          <td style="padding: 24px; text-align: center;">
                            <div style="background-color: #fff3e0; color: #f57c00; border-radius: 12px; width: 56px; height: 56px; line-height: 56px; margin: 0 auto 16px auto; font-size: 20px; font-weight: 600; border: 2px solid #ffcc02;">
                              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" style="vertical-align: middle;">
                                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                              </svg>
                            </div>
                            <h3 style="color: #f57c00; margin: 0 0 8px 0; font-size: 16px; font-weight: 600; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">Questions Handled</h3>
                            <p style="color: #6c757d; margin: 0 0 12px 0; font-size: 18px; font-weight: 700; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                              {{questionsHandled}} Total
                            </p>
                            <div style="font-size: 14px; color: #6c757d; line-height: 1.5; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                              {{#if questionsOrphaned}}
                              <p style="margin: 4px 0;">
                                <svg width="12" height="12" viewBox="0 0 24 24" fill="#6c757d" style="vertical-align: middle; margin-right: 4px;">
                                  <path d="M10.59,13.41C11,13.8 11,14.4 10.59,14.81C10.2,15.2 9.6,15.2 9.19,14.81L7.05,12.67L4.91,14.81C4.5,15.2 3.9,15.2 3.49,14.81C3.08,14.4 3.08,13.8 3.49,13.41L5.63,11.27L3.49,9.13C3.08,8.72 3.08,8.12 3.49,7.71C3.9,7.3 4.5,7.3 4.91,7.71L7.05,9.85L9.19,7.71C9.6,7.3 10.2,7.3 10.61,7.71C11.02,8.12 11.02,8.72 10.61,9.13L8.47,11.27L10.59,13.41M15.5,4L14.5,0H9.5L8.5,4H15.5M6,2V4H18V2H6M6,19V7H18V19A2,2 0 0,1 16,21H8A2,2 0 0,1 6,19Z"/>
                                </svg>
                                Orphaned: {{questionsOrphaned}}
                              </p>
                              {{/if}}
                              {{#if questionsReassigned}}
                              <p style="margin: 4px 0;">
                                <svg width="12" height="12" viewBox="0 0 24 24" fill="#6c757d" style="vertical-align: middle; margin-right: 4px;">
                                  <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
                                </svg>
                                Reassigned: {{questionsReassigned}}
                              </p>
                              {{/if}}
                            </div>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
                {{/if}}
              </td>
            </tr>
          </table>

          <!-- Successfully Deleted Users Section -->
          {{#if successCount}}
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="margin-bottom: 24px;">
            <tr>
              <td style="background-color: #ffffff; padding: 24px; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.08); border: 1px solid #e9ecef; border-left: 4px solid #2e7d32;">
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="#2e7d32" style="margin-right: 8px;">
                    <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
                  </svg>
                  <h2 style="color: #2e7d32; margin: 0; font-size: 18px; font-weight: 600; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                    Successfully Deleted Users
                  </h2>
                </div>
                <p style="color: #6c757d; margin: 0; font-size: 16px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                  {{successCount}} users were successfully removed from the system
                </p>
              </td>
            </tr>
          </table>
          {{/if}}

          <!-- Failed Deletions Section -->
          {{#if failureCount}}
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="margin-bottom: 24px;">
            <tr>
              <td style="background-color: #ffffff; padding: 24px; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.08); border: 1px solid #e9ecef; border-left: 4px solid #d32f2f;">
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="#d32f2f" style="margin-right: 8px;">
                    <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                  </svg>
                  <h2 style="color: #d32f2f; margin: 0; font-size: 18px; font-weight: 600; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                    Failed Deletions
                  </h2>
                </div>
                <p style="color: #6c757d; margin: 0 0 16px 0; font-size: 16px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                  {{failureCount}} users could not be deleted due to database constraints and other issues:
                </p>

                <!-- Modern Failures Details Table -->
                {{#if failures}}
                <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="background-color: #f8f9fa; border-radius: 8px; overflow: hidden; border: 1px solid #e9ecef;">
                  <thead>
                    <tr style="background-color: #e9ecef;">
                      <td style="padding: 16px; font-size: 14px; font-weight: 600; color: #495057; border-bottom: 1px solid #dee2e6; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                        User Email
                      </td>
                      <td style="padding: 16px; font-size: 14px; font-weight: 600; color: #495057; border-bottom: 1px solid #dee2e6; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                        Error Details
                      </td>
                    </tr>
                  </thead>
                  <tbody>
                    {{#each failures}}
                    <tr style="border-bottom: 1px solid #dee2e6;">
                      <td style="padding: 16px; font-size: 14px; color: #495057; vertical-align: top; word-break: break-word; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                        {{email}}
                      </td>
                      <td style="padding: 16px; font-size: 14px; color: #6c757d; vertical-align: top; line-height: 1.5; word-break: break-word; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                        {{#if (contains reason "violates not-null constraint")}}
                          <span style="color: #d32f2f; font-weight: 600;">Database Constraint:</span> Cannot delete user due to related records that require this user reference
                        {{else if (contains reason "foreign key constraint")}}
                          <span style="color: #d32f2f; font-weight: 600;">Foreign Key Constraint:</span> User has dependent records that prevent deletion
                        {{else if (contains reason "violates foreign key constraint")}}
                          <span style="color: #d32f2f; font-weight: 600;">Foreign Key Violation:</span> User has related data that must be handled first
                        {{else}}
                          {{reason}}
                        {{/if}}
                      </td>
                    </tr>
                    {{/each}}
                  </tbody>
                </table>
                {{/if}}
              </td>
            </tr>
          </table>
          {{/if}}

          <!-- Critical Warning Section -->
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="margin-bottom: 24px;">
            <tr>
              <td style="background-color: #ffffff; padding: 24px; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.08); border: 1px solid #e9ecef; border-left: 4px solid #ff9800;">
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="#ff9800" style="margin-right: 8px;">
                    <path d="M13,13H11V7H13M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                  </svg>
                  <h3 style="color: #ff9800; margin: 0; font-size: 16px; font-weight: 600; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                    Critical Notice: Irreversible Action
                  </h3>
                </div>
                <p style="color: #6c757d; margin: 0; font-size: 16px; line-height: 1.5; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                  This bulk deletion operation is <strong style="color: #495057;">permanent and irreversible</strong>. All user data, profiles, and related records have been completely removed from the system and cannot be recovered.
                </p>
              </td>
            </tr>
          </table>

          <!-- Security & Compliance Notice -->
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="margin-bottom: 24px;">
            <tr>
              <td style="background-color: #ffffff; padding: 24px; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.08); border: 1px solid #e9ecef; border-left: 4px solid #1976d2;">
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="#1976d2" style="margin-right: 8px;">
                    <path d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z"/>
                  </svg>
                  <h3 style="color: #1976d2; margin: 0; font-size: 16px; font-weight: 600; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                    Security & Compliance Information
                  </h3>
                </div>
                <p style="color: #6c757d; margin: 0; font-size: 16px; line-height: 1.5; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                  This deletion operation has been logged for security and compliance purposes. All actions are auditable and traceable to the administrator account that initiated the process.
                </p>
              </td>
            </tr>
          </table>

          <!-- Modern Footer Information -->
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0">
            <tr>
              <td style="padding: 24px 0; border-top: 1px solid #e9ecef; text-align: center;">
                <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 12px;">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="#6c757d" style="margin-right: 8px;">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                  </svg>
                  <p style="margin: 0; color: #6c757d; font-size: 14px; font-weight: 600; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                    Report Generated Automatically
                  </p>
                </div>
                <p style="margin: 0 0 8px 0; color: #6c757d; font-size: 14px; line-height: 1.5; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                  This report was generated automatically by the Reach system upon completion of the bulk deletion operation.
                </p>
                <p style="margin: 0; color: #6c757d; font-size: 14px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
                  For technical support or questions about this operation, please contact your system administrator.
                </p>
              </td>
            </tr>
          </table>

        </td>
      </tr>
    </table>

  </td>
</tr>

{{/base}}
