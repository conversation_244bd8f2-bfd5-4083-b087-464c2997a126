import { Test, TestingModule } from '@nestjs/testing';
import { PointSystemRepository } from './point_system.repository';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { CacheService } from '@app/shared/cache/cache.service';
import { RedisService } from '@app/shared/redis/redis.service';
import { getQueueToken } from '@nestjs/bullmq';
import { NotFoundException } from '@nestjs/common';

describe('PointSystemRepository', () => {
  let repository: PointSystemRepository;
  let mockDrizzleService: any;
  let mockLeaderboardQueue: any;

  let mockCacheService: any;
  let mockRedisService: any;
  let mockGeneralQueue: any;

  beforeEach(async () => {
    mockDrizzleService = {
      db: {
        select: jest.fn(),
        insert: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
        transaction: jest.fn((callback) => callback(mockDrizzleService.db)),
      },
    };

    mockLeaderboardQueue = {
      add: jest.fn(),
    };

    mockCacheService = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
    };

    mockRedisService = {
      client: {
        set: jest.fn(),
        del: jest.fn(),
        get: jest.fn(),
      },
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
    };

    mockGeneralQueue = {
      add: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PointSystemRepository,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
        {
          provide: RedisService,
          useValue: mockRedisService,
        },
        {
          provide: getQueueToken('leaderboard'),
          useValue: mockLeaderboardQueue,
        },
        {
          provide: getQueueToken('general'),
          useValue: mockGeneralQueue,
        },
      ],
    }).compile();

    repository = module.get<PointSystemRepository>(PointSystemRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createPointRule', () => {
    it('should create point rule successfully', async () => {
      const ruleData = {
        module: 'quiz',
        action: 'quiz_completion',
        points_config_id: 'config-1',
        frequency: 'once_per_day',
      };
      const mockResult = [{ id: 'rule-1', ...ruleData }];

      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      const result = await repository.createPointRule(ruleData);

      expect(mockDrizzleService.db.insert).toHaveBeenCalled();
      expect(mockInsertQuery.values).toHaveBeenCalledWith(ruleData);
      expect(mockInsertQuery.returning).toHaveBeenCalled();
      expect(mockInsertQuery.execute).toHaveBeenCalled();
      expect(result).toEqual(mockResult[0]); // The method returns result[0]
    });

    it('should handle database errors during creation', async () => {
      const ruleData = {
        module: 'test',
        action: 'test_action',
        points_config_id: 'config-1',
        frequency: 'once_per_day',
      };
      const mockError = new Error('Database constraint violation');

      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockRejectedValue(mockError),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      await expect(repository.createPointRule(ruleData)).rejects.toThrow(
        'Database constraint violation',
      );
    });
  });

  describe('getPointRules', () => {
    it('should get point rules with default parameters', async () => {
      const mockRules = [
        { id: 'rule-1', action: 'quiz_completion', points: 10 },
        { id: 'rule-2', action: 'post_creation', points: 5 },
      ];
      const mockCount = [{ count: 2 }];

      // Mock the main query chain: select().from().where().orderBy().limit().offset()
      const mockMainQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockRules),
      };

      // Mock the count query chain: select().from().where()
      const mockCountQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue(mockCount),
      };

      // Mock the transaction to use the same db object
      mockDrizzleService.db.transaction.mockImplementation((callback: any) =>
        callback(mockDrizzleService.db),
      );

      // Setup the select mock to return different query objects for each call
      // The Promise.all calls select twice: once for main data, once for count
      mockDrizzleService.db.select
        .mockReturnValueOnce(mockMainQuery) // First call for main data query
        .mockReturnValueOnce(mockCountQuery); // Second call for count query

      const result = await repository.getPointRules({
        search: '',
        page: 1,
        limit: 10,
        order: 'asc',
        all: false,
      });

      expect(result).toBeDefined();
      expect(result.data).toEqual(mockRules);
      expect(result.total).toBe(2);
    });

    it('should handle search filter', async () => {
      const filters = {
        search: 'quiz',
        limit: 10,
        page: 1,
        order: 'asc' as const,
        all: false,
      };
      const mockRules = [
        { id: 'rule-1', action: 'quiz_completion', points: 10 },
      ];
      const mockCount = [{ count: 1 }];

      // Mock the main query chain: select().from().where().orderBy().limit().offset()
      const mockMainQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockRules),
      };

      // Mock the count query chain: select().from().where()
      const mockCountQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue(mockCount),
      };

      // Mock the transaction to use the same db object
      mockDrizzleService.db.transaction.mockImplementation((callback: any) =>
        callback(mockDrizzleService.db),
      );

      // Setup the select mock to return different query objects for each call
      mockDrizzleService.db.select
        .mockReturnValueOnce(mockMainQuery) // First call for main data query
        .mockReturnValueOnce(mockCountQuery); // Second call for count query

      const result = await repository.getPointRules(filters);

      expect(result.data).toEqual(mockRules);
      expect(result.total).toBe(1);
    });
  });

  describe('updatePointRule', () => {
    it('should update point rule successfully', async () => {
      const ruleId = 'rule-1';
      const updateData = {
        module: 'quiz',
        action: 'quiz_completion',
        points_config_id: 'config-1',
        frequency: 'once_per_day',
      };
      const mockResult = [{ id: ruleId, ...updateData }];

      const mockUpdateQuery = {
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.update.mockReturnValue(mockUpdateQuery);

      const result = await repository.updatePointRule(ruleId, updateData);

      expect(mockDrizzleService.db.update).toHaveBeenCalled();
      expect(mockUpdateQuery.set).toHaveBeenCalledWith(updateData);
      expect(mockUpdateQuery.where).toHaveBeenCalled();
      expect(mockUpdateQuery.returning).toHaveBeenCalled();
      expect(result).toEqual(mockResult);
    });

    it('should throw NotFoundException when rule not found', async () => {
      const ruleId = 'non-existent';
      const updateData = {
        module: 'quiz',
        action: 'quiz_completion',
        points_config_id: 'config-1',
        frequency: 'once_per_day',
      };

      const mockUpdateQuery = {
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([]),
      };
      mockDrizzleService.db.update.mockReturnValue(mockUpdateQuery);

      await expect(
        repository.updatePointRule(ruleId, updateData),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('deletePointRule', () => {
    it('should delete point rule successfully', async () => {
      const ruleId = 'rule-1';

      const mockDeleteQuery = {
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ rowCount: 1 }),
      };
      mockDrizzleService.db.delete.mockReturnValue(mockDeleteQuery);

      const result = await repository.deletePointRule(ruleId);

      expect(mockDrizzleService.db.delete).toHaveBeenCalled();
      expect(mockDeleteQuery.where).toHaveBeenCalled();
      expect(result).toEqual({ message: 'Point rule deleted successfully' });
    });

    it('should throw NotFoundException when rule not found for deletion', async () => {
      const ruleId = 'non-existent';

      const mockDeleteQuery = {
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ rowCount: 0 }),
      };
      mockDrizzleService.db.delete.mockReturnValue(mockDeleteQuery);

      await expect(repository.deletePointRule(ruleId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('awardPoints', () => {
    it('should award points successfully', async () => {
      const pointData = {
        student_id: 'student-1',
        point_rule_id: 'rule-1',
        points: 10,
        description: 'Quiz completed',
      };
      const mockResult = [{ id: 'log-1', ...pointData }];

      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      const result = await repository.awardPoints(pointData);

      expect(mockDrizzleService.db.insert).toHaveBeenCalled();
      expect(mockInsertQuery.values).toHaveBeenCalledWith(pointData);
      expect(mockInsertQuery.returning).toHaveBeenCalled();
      expect(result).toEqual(mockResult);
    });

    it('should handle errors during point awarding', async () => {
      const pointData = {
        student_id: 'student-1',
        point_rule_id: 'rule-1',
        points: 5,
        description: 'Test points',
      };
      const mockError = new Error('Database error');

      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockRejectedValue(mockError),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      await expect(repository.awardPoints(pointData)).rejects.toThrow(
        'Database error',
      );
    });
  });

  describe('getStudentPoints', () => {
    it('should get student total points', async () => {
      const studentId = 'student-1';
      const mockResult = [{ totalPoints: 150 }];

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const result = await repository.getStudentPoints(studentId);

      expect(mockDrizzleService.db.select).toHaveBeenCalled();
      expect(mockQuery.from).toHaveBeenCalled();
      expect(mockQuery.where).toHaveBeenCalled();
      expect(result).toBe(150);
    });

    it('should return 0 when student has no points', async () => {
      const studentId = 'student-new';
      const mockResult = [{ totalPoints: null }];

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const result = await repository.getStudentPoints(studentId);

      expect(result).toBe(0);
    });
  });

  describe('getPointsLogs', () => {
    it('should get points logs with pagination', async () => {
      const filters = {
        limit: 10,
        page: 1,
        search: '',
        order: 'asc' as const,
        all: false,
      };
      const mockLogs = [
        { id: 'log-1', student_id: 'student-1', points: 10, action: 'quiz' },
        { id: 'log-2', student_id: 'student-2', points: 5, action: 'post' },
      ];
      const mockCount = [{ count: 2 }];

      // Create a more sophisticated mock that handles the exact query chain
      // The actual query has: select().from().leftJoin().leftJoin().where().orderBy().limit().offset()
      const mockMainQuery = {
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(), // First leftJoin
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockLogs),
      };

      // The count query has: select().from().leftJoin().leftJoin().where()
      const mockCountQuery = {
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(), // First leftJoin
        where: jest.fn().mockResolvedValue(mockCount),
      };

      // Mock the transaction to use the same db object
      mockDrizzleService.db.transaction.mockImplementation((callback: any) =>
        callback(mockDrizzleService.db),
      );

      // Setup the select mock to return different query objects for each call
      // The Promise.all calls select twice: once for count, once for data
      mockDrizzleService.db.select
        .mockReturnValueOnce(mockMainQuery) // First call for main data query
        .mockReturnValueOnce(mockCountQuery); // Second call for count query

      const result = await repository.getPointsLogs(filters);

      expect(result).toBeDefined();
      expect(result.data).toEqual(mockLogs);
      expect(result.total).toBe(2);
    });

    it('should handle student filter', async () => {
      const filters = {
        studentId: 'student-1',
        limit: 10,
        page: 1,
        search: '',
        order: 'asc' as const,
        all: false,
      };
      const mockLogs = [{ id: 'log-1', student_id: 'student-1', points: 10 }];
      const mockCount = [{ count: 1 }];

      // Create a more sophisticated mock that handles the exact query chain
      const mockMainQuery = {
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(), // First leftJoin
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockLogs),
      };

      const mockCountQuery = {
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(), // First leftJoin
        where: jest.fn().mockResolvedValue(mockCount),
      };

      // Mock the transaction to use the same db object
      mockDrizzleService.db.transaction.mockImplementation((callback: any) =>
        callback(mockDrizzleService.db),
      );

      // Setup the select mock to return different query objects for each call
      mockDrizzleService.db.select
        .mockReturnValueOnce(mockMainQuery) // First call for main data query
        .mockReturnValueOnce(mockCountQuery); // Second call for count query

      const result = await repository.getPointsLogs(filters);

      expect(result.data).toEqual(mockLogs);
      expect(result.total).toBe(1);
    });
  });

  describe('awardPointsToStudent', () => {
    it('should award points successfully with valid inputs', async () => {
      const pointModule = 'quiz';
      const action = 'completion';
      const studentId = 'student-1';

      // Mock student exists check
      const mockStudentQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue([{ id: studentId }]),
      };
      mockDrizzleService.db.select.mockReturnValueOnce(mockStudentQuery);

      // Mock Redis lock acquisition
      mockRedisService.client.set.mockResolvedValue('OK');
      mockRedisService.client.del.mockResolvedValue(1);

      // Mock transaction and processPointsAward
      const mockResult = { id: 'log-1', points: 10 };
      mockDrizzleService.db.transaction.mockImplementation(
        async (callback: any) => {
          return callback(mockDrizzleService.db);
        },
      );

      // Mock the internal methods that processPointsAward calls
      jest.spyOn(repository, 'getPointRule').mockResolvedValue({
        id: 'rule-1',
        frequency: 'once_per_day',
        points_config_id: 'config-1',
      });

      jest.spyOn(repository, 'getPointsConfigById').mockResolvedValue([
        {
          point_value: 10,
          description: 'Quiz completion points',
        },
      ]);

      jest.spyOn(repository as any, 'checkFrequency').mockResolvedValue(true);
      jest.spyOn(repository, 'rewardPoints').mockResolvedValue(mockResult);
      jest
        .spyOn(repository as any, 'updateRealtimeLeaderboard')
        .mockResolvedValue(undefined);
      jest
        .spyOn(repository as any, 'invalidateLeaderboardCache')
        .mockResolvedValue(undefined);

      const result = await repository.awardPointsToStudent(
        pointModule,
        action,
        studentId,
      );

      expect(result).toEqual(mockResult);
      expect(mockRedisService.client.set).toHaveBeenCalledWith(
        expect.stringContaining('lock:points:'),
        'locked',
        'PX',
        5000,
        'NX',
      );
      expect(mockRedisService.client.del).toHaveBeenCalled();
    });

    it('should return null for invalid student ID', async () => {
      const result1 = await repository.awardPointsToStudent(
        'quiz',
        'completion',
        '',
      );
      const result2 = await repository.awardPointsToStudent(
        'quiz',
        'completion',
        null as any,
      );
      const result3 = await repository.awardPointsToStudent(
        'quiz',
        'completion',
        '   ',
      );

      expect(result1).toBeNull();
      expect(result2).toBeNull();
      expect(result3).toBeNull();
    });

    it('should return null when student does not exist', async () => {
      const pointModule = 'quiz';
      const action = 'completion';
      const studentId = 'non-existent-student';

      // Mock student does not exist
      const mockStudentQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue([]), // Empty array means student not found
      };
      mockDrizzleService.db.select.mockReturnValueOnce(mockStudentQuery);

      const result = await repository.awardPointsToStudent(
        pointModule,
        action,
        studentId,
      );

      expect(result).toBeNull();
    });

    it('should return null when Redis lock acquisition fails', async () => {
      const pointModule = 'quiz';
      const action = 'completion';
      const studentId = 'student-1';

      // Mock student exists check
      const mockStudentQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue([{ id: studentId }]),
      };
      mockDrizzleService.db.select.mockReturnValueOnce(mockStudentQuery);

      // Mock Redis lock acquisition failure
      mockRedisService.client.set.mockResolvedValue(null);

      const result = await repository.awardPointsToStudent(
        pointModule,
        action,
        studentId,
      );

      expect(result).toBeNull();
    });

    it('should handle errors and release lock', async () => {
      const pointModule = 'quiz';
      const action = 'completion';
      const studentId = 'student-1';

      // Mock student exists check
      const mockStudentQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue([{ id: studentId }]),
      };
      mockDrizzleService.db.select.mockReturnValueOnce(mockStudentQuery);

      // Mock Redis lock acquisition
      mockRedisService.client.set.mockResolvedValue('OK');
      mockRedisService.client.del.mockResolvedValue(1);

      // Mock transaction error
      const mockError = new Error('Transaction failed');
      mockDrizzleService.db.transaction.mockRejectedValue(mockError);

      await expect(
        repository.awardPointsToStudent(pointModule, action, studentId),
      ).rejects.toThrow('Transaction failed');

      // Verify lock was released
      expect(mockRedisService.client.del).toHaveBeenCalled();
    });
  });

  describe('checkFrequency', () => {
    it('should return true for EVERY_EVENT frequency', async () => {
      const result = await (repository as any).checkFrequency(
        'Every event',
        'student-1',
        'rule-1',
        mockDrizzleService.db,
      );

      expect(result).toBe(true);
    });

    it('should return true for ONCE frequency when no previous award exists', async () => {
      // Mock no previous award found
      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([]), // No previous award
      };
      mockDrizzleService.db.select.mockReturnValueOnce(mockQuery);

      const result = await (repository as any).checkFrequency(
        'Once',
        'student-1',
        'rule-1',
        mockDrizzleService.db,
      );

      expect(result).toBe(true);
    });

    it('should return false for ONCE frequency when previous award exists', async () => {
      // Mock previous award found
      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([{ id: 'existing-award' }]),
      };
      mockDrizzleService.db.select.mockReturnValueOnce(mockQuery);

      const result = await (repository as any).checkFrequency(
        'Once',
        'student-1',
        'rule-1',
        mockDrizzleService.db,
      );

      expect(result).toBe(false);
    });

    it('should return true for ONCE_A_DAY frequency when no award today', async () => {
      // Mock no award today
      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([]),
      };
      mockDrizzleService.db.select.mockReturnValueOnce(mockQuery);

      const result = await (repository as any).checkFrequency(
        'Once Daily',
        'student-1',
        'rule-1',
        mockDrizzleService.db,
      );

      expect(result).toBe(true);
    });

    it('should return false for ONCE_A_DAY frequency when award exists today', async () => {
      // Mock award exists today
      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([{ id: 'today-award' }]),
      };
      mockDrizzleService.db.select.mockReturnValueOnce(mockQuery);

      const result = await (repository as any).checkFrequency(
        'Once Daily',
        'student-1',
        'rule-1',
        mockDrizzleService.db,
      );

      expect(result).toBe(false);
    });

    it('should return true for ONCE_A_WEEK frequency when no award this week', async () => {
      // Mock no award this week
      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([]),
      };
      mockDrizzleService.db.select.mockReturnValueOnce(mockQuery);

      const result = await (repository as any).checkFrequency(
        'Once a week',
        'student-1',
        'rule-1',
        mockDrizzleService.db,
      );

      expect(result).toBe(true);
    });

    it('should return true for ONCE_A_MONTH frequency when no award this month', async () => {
      // Mock no award this month
      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([]),
      };
      mockDrizzleService.db.select.mockReturnValueOnce(mockQuery);

      const result = await (repository as any).checkFrequency(
        'Once a month',
        'student-1',
        'rule-1',
        mockDrizzleService.db,
      );

      expect(result).toBe(true);
    });

    it('should throw error for unknown frequency', async () => {
      await expect(
        (repository as any).checkFrequency(
          'unknown_frequency',
          'student-1',
          'rule-1',
          mockDrizzleService.db,
        ),
      ).rejects.toThrow('Invalid frequency');
    });
  });

  describe('createPointConfiguration', () => {
    it('should create point configuration successfully', async () => {
      const configData = {
        point_name: 'Quiz Points',
        point_value: 10,
        description: 'Points for quiz completion',
      };
      const mockResult = [{ id: 'config-1', ...configData }];

      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      const result = await repository.createPointConfiguration(configData);

      expect(mockDrizzleService.db.insert).toHaveBeenCalled();
      expect(mockInsertQuery.values).toHaveBeenCalledWith(configData);
      expect(result).toEqual(mockResult);
    });

    it('should handle database errors during configuration creation', async () => {
      const configData = {
        point_name: 'Test Points',
        point_value: 5,
        description: 'Test description',
      };
      const mockError = new Error('Database constraint violation');

      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockRejectedValue(mockError),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      await expect(
        repository.createPointConfiguration(configData),
      ).rejects.toThrow('Database constraint violation');
    });
  });

  describe('getPointsConfigById', () => {
    it('should get point configuration by ID successfully', async () => {
      const configId = 'config-1';
      const mockConfig = [
        { id: configId, point_name: 'Quiz Points', point_value: 10 },
      ];

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue(mockConfig),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const result = await repository.getPointsConfigById(configId);

      expect(result).toEqual(mockConfig);
      expect(mockQuery.where).toHaveBeenCalled();
    });

    it('should handle errors when getting point configuration', async () => {
      const configId = 'config-1';
      const mockError = new Error('Database error');

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockRejectedValue(mockError),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      await expect(repository.getPointsConfigById(configId)).rejects.toThrow(
        'Database error',
      );
    });
  });

  describe('removePointConfiguration', () => {
    it('should remove point configuration successfully', async () => {
      const configId = 'config-1';

      const mockDeleteQuery = {
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ rowCount: 1 }),
      };
      mockDrizzleService.db.delete.mockReturnValue(mockDeleteQuery);

      const result = await repository.removePointConfiguration(configId);

      expect(mockDrizzleService.db.delete).toHaveBeenCalled();
      expect(mockDeleteQuery.where).toHaveBeenCalled();
      expect(result).toEqual({ message: 'Points deleted successfully' });
    });

    it('should throw NotFoundException when configuration not found', async () => {
      const configId = 'non-existent';

      const mockDeleteQuery = {
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ rowCount: 0 }),
      };
      mockDrizzleService.db.delete.mockReturnValue(mockDeleteQuery);

      await expect(
        repository.removePointConfiguration(configId),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('verifyPointsAwarded', () => {
    it('should return false when point rule does not exist', async () => {
      jest.spyOn(repository, 'getPointRule').mockResolvedValue(null);

      const result = await repository.verifyPointsAwarded(
        'student-1',
        'quiz',
        'completion',
      );

      expect(result).toBe(false);
    });

    it('should return true when points were awarded within frequency period', async () => {
      const mockPointRule = {
        id: 'rule-1',
        frequency: 'Once Daily',
        points_config_id: 'config-1',
      };

      jest.spyOn(repository, 'getPointRule').mockResolvedValue(mockPointRule);

      // Mock points found within period
      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([{ id: 'existing-award' }]),
      };
      mockDrizzleService.db.select.mockReturnValueOnce(mockQuery);

      const result = await repository.verifyPointsAwarded(
        'student-1',
        'quiz',
        'completion',
      );

      expect(result).toBe(true);
    });

    it('should return false when no points were awarded within frequency period', async () => {
      const mockPointRule = {
        id: 'rule-1',
        frequency: 'Once Daily',
        points_config_id: 'config-1',
      };

      jest.spyOn(repository, 'getPointRule').mockResolvedValue(mockPointRule);

      // Mock no points found within period
      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([]),
      };
      mockDrizzleService.db.select.mockReturnValueOnce(mockQuery);

      const result = await repository.verifyPointsAwarded(
        'student-1',
        'quiz',
        'completion',
      );

      expect(result).toBe(false);
    });

    it('should handle errors gracefully', async () => {
      jest
        .spyOn(repository, 'getPointRule')
        .mockRejectedValue(new Error('Database error'));

      const result = await repository.verifyPointsAwarded(
        'student-1',
        'quiz',
        'completion',
      );

      expect(result).toBe(false);
    });
  });

  describe('removePointsFromStudent', () => {
    it('should remove points successfully', async () => {
      const pointModule = 'quiz';
      const action = 'completion';
      const studentId = 'student-1';

      const mockPointRule = {
        id: 'rule-1',
        points_config_id: 'config-1',
      };

      const mockPointsConfig = [
        {
          point_value: 10,
          description: 'Quiz completion points',
        },
      ];

      jest.spyOn(repository, 'getPointRule').mockResolvedValue(mockPointRule);
      jest
        .spyOn(repository, 'getPointsConfigById')
        .mockResolvedValue(mockPointsConfig);

      // Mock the insert query for negative points
      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([{ id: 'log-1', points: -10 }]),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      await repository.removePointsFromStudent(pointModule, action, studentId);

      expect(repository.getPointRule).toHaveBeenCalledWith(
        pointModule,
        action,
        mockDrizzleService.db,
      );
      expect(repository.getPointsConfigById).toHaveBeenCalledWith(
        'config-1',
        mockDrizzleService.db,
      );
      expect(mockDrizzleService.db.insert).toHaveBeenCalled();
      expect(mockInsertQuery.values).toHaveBeenCalledWith({
        point_rule_id: 'rule-1',
        points: -10,
        description: 'Reversal of points: Quiz completion points',
        student_id: studentId,
      });
    });

    it('should throw NotFoundException when point rule not found', async () => {
      jest.spyOn(repository, 'getPointRule').mockResolvedValue(null);

      await expect(
        repository.removePointsFromStudent('quiz', 'completion', 'student-1'),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException when points configuration not found', async () => {
      const mockPointRule = {
        id: 'rule-1',
        points_config_id: 'config-1',
      };

      jest.spyOn(repository, 'getPointRule').mockResolvedValue(mockPointRule);
      jest.spyOn(repository, 'getPointsConfigById').mockResolvedValue(null);

      await expect(
        repository.removePointsFromStudent('quiz', 'completion', 'student-1'),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('getAllPointConfiguration', () => {
    it('should get all point configurations with pagination', async () => {
      const filters = {
        search: '',
        page: 1,
        limit: 10,
        order: 'asc' as const,
        all: false,
      };
      const mockConfigs = [
        { id: 'config-1', point_name: 'Quiz Points', point_value: 10 },
        { id: 'config-2', point_name: 'Post Points', point_value: 5 },
      ];
      const mockCount = [{ count: 2 }];

      // Mock the main query chain
      const mockMainQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockConfigs),
      };

      // Mock the count query chain
      const mockCountQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue(mockCount),
      };

      // Mock the transaction to use the same db object
      mockDrizzleService.db.transaction.mockImplementation((callback: any) =>
        callback(mockDrizzleService.db),
      );

      // Setup the select mock to return different query objects for each call
      mockDrizzleService.db.select
        .mockReturnValueOnce(mockMainQuery) // First call for main data query
        .mockReturnValueOnce(mockCountQuery); // Second call for count query

      const result = await repository.getAllPointConfiguration(filters);

      expect(result).toBeDefined();
      expect(result.data).toEqual(mockConfigs);
      expect(result.total).toBe(2);
    });
  });

  describe('updatePointConfiguration', () => {
    it('should update point configuration successfully', async () => {
      const configId = 'config-1';
      const updateData = {
        point_name: 'Updated Quiz Points',
        point_value: 15,
        description: 'Updated description',
      };
      const mockResult = [{ id: configId, ...updateData }];

      const mockUpdateQuery = {
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.update.mockReturnValue(mockUpdateQuery);

      const result = await repository.updatePointConfiguration(
        configId,
        updateData,
      );

      expect(mockDrizzleService.db.update).toHaveBeenCalled();
      expect(mockUpdateQuery.set).toHaveBeenCalledWith(updateData);
      expect(mockUpdateQuery.where).toHaveBeenCalled();
      expect(result).toEqual(mockResult);
    });

    it('should handle errors during update', async () => {
      const configId = 'config-1';
      const updateData = {
        point_name: 'Test',
        point_value: 5,
        description: 'Test',
      };
      const mockError = new Error('Database error');

      const mockUpdateQuery = {
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockRejectedValue(mockError),
      };
      mockDrizzleService.db.update.mockReturnValue(mockUpdateQuery);

      await expect(
        repository.updatePointConfiguration(configId, updateData),
      ).rejects.toThrow('Database error');
    });
  });

  describe('getPointRule', () => {
    it('should get point rule successfully', async () => {
      const module = 'quiz';
      const action = 'completion';
      const mockRule = {
        id: 'rule-1',
        module,
        action,
        points_config_id: 'config-1',
        frequency: 'Once Daily',
      };

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([mockRule]),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const result = await repository.getPointRule(module, action);

      expect(result).toEqual(mockRule);
      expect(mockQuery.where).toHaveBeenCalled();
    });

    it('should return null when point rule not found', async () => {
      const module = 'quiz';
      const action = 'completion';

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([]),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const result = await repository.getPointRule(module, action);

      expect(result).toBeNull();
    });

    it('should handle errors gracefully', async () => {
      const module = 'quiz';
      const action = 'completion';
      const mockError = new Error('Database error');

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        execute: jest.fn().mockRejectedValue(mockError),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      await expect(repository.getPointRule(module, action)).rejects.toThrow(
        'Database error',
      );
    });
  });

  describe('removePoints', () => {
    it('should remove points successfully', async () => {
      const pointId = 'log-1';

      const mockDeleteQuery = {
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ rowCount: 1 }),
      };
      mockDrizzleService.db.delete.mockReturnValue(mockDeleteQuery);

      const result = await repository.removePoints(pointId);

      expect(mockDrizzleService.db.delete).toHaveBeenCalled();
      expect(mockDeleteQuery.where).toHaveBeenCalled();
      expect(result).toEqual({ message: 'Point logs remove successfully' });
    });

    it('should throw NotFoundException when point log not found', async () => {
      const pointId = 'non-existent';

      const mockDeleteQuery = {
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ rowCount: 0 }),
      };
      mockDrizzleService.db.delete.mockReturnValue(mockDeleteQuery);

      await expect(repository.removePoints(pointId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('verifyLoginPointsAwarded', () => {
    it('should verify login points awarded', async () => {
      const studentId = 'student-1';

      jest.spyOn(repository, 'verifyPointsAwarded').mockResolvedValue(true);

      const result = await repository.verifyLoginPointsAwarded(studentId);

      expect(repository.verifyPointsAwarded).toHaveBeenCalledWith(
        studentId,
        'User',
        'Login',
      );
      expect(result).toBe(true);
    });
  });

  describe('disablePointRule', () => {
    it('should disable point rule successfully', async () => {
      const ruleId = 'rule-1';

      const mockUpdateQuery = {
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([{ id: ruleId, disabled: true }]),
      };
      mockDrizzleService.db.update.mockReturnValue(mockUpdateQuery);

      const result = await repository.disablePointRule(ruleId);

      expect(mockDrizzleService.db.update).toHaveBeenCalled();
      expect(mockUpdateQuery.set).toHaveBeenCalledWith({ disabled: true });
      expect(mockUpdateQuery.where).toHaveBeenCalled();
      expect(result).toEqual({ message: 'Point rule disabled successfully' });
    });

    it('should throw NotFoundException when rule not found', async () => {
      const ruleId = 'non-existent';

      const mockUpdateQuery = {
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([]),
      };
      mockDrizzleService.db.update.mockReturnValue(mockUpdateQuery);

      await expect(repository.disablePointRule(ruleId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('getStudentPoints error handling', () => {
    it('should return 0 when database error occurs', async () => {
      const studentId = 'student-1';
      const mockError = new Error('Database connection failed');

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockRejectedValue(mockError),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const result = await repository.getStudentPoints(studentId);

      expect(result).toBe(0);
    });
  });

  describe('reversePointsForAction', () => {
    it('should reverse points for action successfully', async () => {
      const pointModule = 'Post';
      const originalAction = 'Like';
      const studentId = 'student-1';

      const mockPointRule = {
        id: 'rule-1',
        points_config_id: 'config-1',
      };

      const mockPointsConfig = [
        {
          point_value: 5,
          description: 'Like points',
        },
      ];

      jest.spyOn(repository, 'getPointRule').mockResolvedValue(mockPointRule);
      jest
        .spyOn(repository, 'getPointsConfigById')
        .mockResolvedValue(mockPointsConfig);

      // Mock the insert query for negative points
      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([{ id: 'log-1', points: -5 }]),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      // Mock cache invalidation and leaderboard update
      jest
        .spyOn(repository as any, 'invalidateLeaderboardCache')
        .mockResolvedValue(undefined);
      jest
        .spyOn(repository as any, 'updateRealtimeLeaderboard')
        .mockResolvedValue(undefined);

      await repository.reversePointsForAction(
        pointModule,
        originalAction,
        studentId,
      );

      expect(repository.getPointRule).toHaveBeenCalledWith(
        pointModule,
        originalAction,
        mockDrizzleService.db,
      );
      expect(repository.getPointsConfigById).toHaveBeenCalledWith(
        'config-1',
        mockDrizzleService.db,
      );
      expect(mockDrizzleService.db.insert).toHaveBeenCalled();
      expect(mockInsertQuery.values).toHaveBeenCalledWith({
        point_rule_id: 'rule-1',
        points: -5,
        description: 'Reversal: Like points',
        student_id: studentId,
      });
    });

    it('should throw NotFoundException when point rule not found', async () => {
      jest.spyOn(repository, 'getPointRule').mockResolvedValue(null);

      await expect(
        repository.reversePointsForAction('Post', 'Like', 'student-1'),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('verifyNetPointsAwarded', () => {
    it('should return false when point rule does not exist', async () => {
      jest.spyOn(repository, 'getPointRule').mockResolvedValue(null);

      const result = await repository.verifyNetPointsAwarded(
        'student-1',
        'Post',
        'Like',
      );

      expect(result).toBe(false);
    });

    it('should return true when net points are positive', async () => {
      const mockPointRule = {
        id: 'rule-1',
        frequency: 'Once Daily',
        points_config_id: 'config-1',
      };

      jest.spyOn(repository, 'getPointRule').mockResolvedValue(mockPointRule);

      // Mock positive net points
      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([{ totalPoints: 10 }]),
      };
      mockDrizzleService.db.select.mockReturnValueOnce(mockQuery);

      const result = await repository.verifyNetPointsAwarded(
        'student-1',
        'Post',
        'Like',
      );

      expect(result).toBe(true);
    });

    it('should return false when net points are zero or negative', async () => {
      const mockPointRule = {
        id: 'rule-1',
        frequency: 'Every event',
        points_config_id: 'config-1',
      };

      jest.spyOn(repository, 'getPointRule').mockResolvedValue(mockPointRule);

      // Mock zero net points
      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([{ totalPoints: 0 }]),
      };
      mockDrizzleService.db.select.mockReturnValueOnce(mockQuery);

      const result = await repository.verifyNetPointsAwarded(
        'student-1',
        'Post',
        'Like',
      );

      expect(result).toBe(false);
    });

    it('should handle errors gracefully', async () => {
      jest
        .spyOn(repository, 'getPointRule')
        .mockRejectedValue(new Error('Database error'));

      const result = await repository.verifyNetPointsAwarded(
        'student-1',
        'Post',
        'Like',
      );

      expect(result).toBe(false);
    });
  });
});
