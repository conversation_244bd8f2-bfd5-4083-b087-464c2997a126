import { Test, TestingModule } from '@nestjs/testing';
import { PointSystemRepository } from './point_system.repository';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { CacheService } from '@app/shared/cache/cache.service';
import { RedisService } from '@app/shared/redis/redis.service';
import { getQueueToken } from '@nestjs/bullmq';
import { NotFoundException } from '@nestjs/common';

describe('PointSystemRepository', () => {
  let repository: PointSystemRepository;
  let mockDrizzleService: any;
  let mockLeaderboardQueue: any;

  beforeEach(async () => {
    mockDrizzleService = {
      db: {
        select: jest.fn(),
        insert: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
        transaction: jest.fn((callback) => callback(mockDrizzleService.db)),
      },
    };

    mockLeaderboardQueue = {
      add: jest.fn(),
    };

    const mockCacheService = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
    };

    const mockRedisService = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
    };

    const mockGeneralQueue = {
      add: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PointSystemRepository,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
        {
          provide: RedisService,
          useValue: mockRedisService,
        },
        {
          provide: getQueueToken('leaderboard'),
          useValue: mockLeaderboardQueue,
        },
        {
          provide: getQueueToken('general'),
          useValue: mockGeneralQueue,
        },
      ],
    }).compile();

    repository = module.get<PointSystemRepository>(PointSystemRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createPointRule', () => {
    it('should create point rule successfully', async () => {
      const ruleData = {
        module: 'quiz',
        action: 'quiz_completion',
        points_config_id: 'config-1',
        frequency: 'once_per_day',
      };
      const mockResult = [{ id: 'rule-1', ...ruleData }];

      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      const result = await repository.createPointRule(ruleData);

      expect(mockDrizzleService.db.insert).toHaveBeenCalled();
      expect(mockInsertQuery.values).toHaveBeenCalledWith(ruleData);
      expect(mockInsertQuery.returning).toHaveBeenCalled();
      expect(mockInsertQuery.execute).toHaveBeenCalled();
      expect(result).toEqual(mockResult[0]); // The method returns result[0]
    });

    it('should handle database errors during creation', async () => {
      const ruleData = {
        module: 'test',
        action: 'test_action',
        points_config_id: 'config-1',
        frequency: 'once_per_day',
      };
      const mockError = new Error('Database constraint violation');

      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockRejectedValue(mockError),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      await expect(repository.createPointRule(ruleData)).rejects.toThrow(
        'Database constraint violation',
      );
    });
  });

  describe('getPointRules', () => {
    it('should get point rules with default parameters', async () => {
      const mockRules = [
        { id: 'rule-1', action: 'quiz_completion', points: 10 },
        { id: 'rule-2', action: 'post_creation', points: 5 },
      ];
      const mockCount = [{ count: 2 }];

      // Mock the main query chain: select().from().where().orderBy().limit().offset()
      const mockMainQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockRules),
      };

      // Mock the count query chain: select().from().where()
      const mockCountQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue(mockCount),
      };

      // Mock the transaction to use the same db object
      mockDrizzleService.db.transaction.mockImplementation((callback: any) =>
        callback(mockDrizzleService.db),
      );

      // Setup the select mock to return different query objects for each call
      // The Promise.all calls select twice: once for main data, once for count
      mockDrizzleService.db.select
        .mockReturnValueOnce(mockMainQuery) // First call for main data query
        .mockReturnValueOnce(mockCountQuery); // Second call for count query

      const result = await repository.getPointRules({
        search: '',
        page: 1,
        limit: 10,
        order: 'asc',
        all: false,
      });

      expect(result).toBeDefined();
      expect(result.data).toEqual(mockRules);
      expect(result.total).toBe(2);
    });

    it('should handle search filter', async () => {
      const filters = {
        search: 'quiz',
        limit: 10,
        page: 1,
        order: 'asc' as const,
        all: false,
      };
      const mockRules = [
        { id: 'rule-1', action: 'quiz_completion', points: 10 },
      ];
      const mockCount = [{ count: 1 }];

      // Mock the main query chain
      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockRules),
      };

      // Mock the count query chain
      const mockCountQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue(mockCount),
      };

      // Setup the select mock to return different query objects for each call
      mockDrizzleService.db.select
        .mockReturnValueOnce(mockCountQuery) // First call for count
        .mockReturnValueOnce(mockQuery); // Second call for data

      const result = await repository.getPointRules(filters);

      expect(result.data).toEqual(mockRules);
      expect(result.total).toBe(1);
    });
  });

  describe('updatePointRule', () => {
    it('should update point rule successfully', async () => {
      const ruleId = 'rule-1';
      const updateData = {
        module: 'quiz',
        action: 'quiz_completion',
        points_config_id: 'config-1',
        frequency: 'once_per_day',
      };
      const mockResult = [{ id: ruleId, ...updateData }];

      const mockUpdateQuery = {
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.update.mockReturnValue(mockUpdateQuery);

      const result = await repository.updatePointRule(ruleId, updateData);

      expect(mockDrizzleService.db.update).toHaveBeenCalled();
      expect(mockUpdateQuery.set).toHaveBeenCalledWith(updateData);
      expect(mockUpdateQuery.where).toHaveBeenCalled();
      expect(mockUpdateQuery.returning).toHaveBeenCalled();
      expect(result).toEqual(mockResult);
    });

    it('should throw NotFoundException when rule not found', async () => {
      const ruleId = 'non-existent';
      const updateData = {
        module: 'quiz',
        action: 'quiz_completion',
        points_config_id: 'config-1',
        frequency: 'once_per_day',
      };

      const mockUpdateQuery = {
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([]),
      };
      mockDrizzleService.db.update.mockReturnValue(mockUpdateQuery);

      await expect(
        repository.updatePointRule(ruleId, updateData),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('deletePointRule', () => {
    it('should delete point rule successfully', async () => {
      const ruleId = 'rule-1';

      const mockDeleteQuery = {
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ rowCount: 1 }),
      };
      mockDrizzleService.db.delete.mockReturnValue(mockDeleteQuery);

      const result = await repository.deletePointRule(ruleId);

      expect(mockDrizzleService.db.delete).toHaveBeenCalled();
      expect(mockDeleteQuery.where).toHaveBeenCalled();
      expect(result).toEqual({ message: 'Point rule deleted successfully' });
    });

    it('should throw NotFoundException when rule not found for deletion', async () => {
      const ruleId = 'non-existent';

      const mockDeleteQuery = {
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ rowCount: 0 }),
      };
      mockDrizzleService.db.delete.mockReturnValue(mockDeleteQuery);

      await expect(repository.deletePointRule(ruleId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('awardPoints', () => {
    it('should award points successfully', async () => {
      const pointData = {
        student_id: 'student-1',
        point_rule_id: 'rule-1',
        points: 10,
        description: 'Quiz completed',
      };
      const mockResult = [{ id: 'log-1', ...pointData }];

      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      const result = await repository.awardPoints(pointData);

      expect(mockDrizzleService.db.insert).toHaveBeenCalled();
      expect(mockInsertQuery.values).toHaveBeenCalledWith(pointData);
      expect(mockInsertQuery.returning).toHaveBeenCalled();
      expect(result).toEqual(mockResult);
    });

    it('should handle errors during point awarding', async () => {
      const pointData = {
        student_id: 'student-1',
        point_rule_id: 'rule-1',
        points: 5,
        description: 'Test points',
      };
      const mockError = new Error('Database error');

      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockRejectedValue(mockError),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      await expect(repository.awardPoints(pointData)).rejects.toThrow(
        'Database error',
      );
    });
  });

  describe('getStudentPoints', () => {
    it('should get student total points', async () => {
      const studentId = 'student-1';
      const mockResult = [{ totalPoints: 150 }];

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const result = await repository.getStudentPoints(studentId);

      expect(mockDrizzleService.db.select).toHaveBeenCalled();
      expect(mockQuery.from).toHaveBeenCalled();
      expect(mockQuery.where).toHaveBeenCalled();
      expect(result).toBe(150);
    });

    it('should return 0 when student has no points', async () => {
      const studentId = 'student-new';
      const mockResult = [{ totalPoints: null }];

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const result = await repository.getStudentPoints(studentId);

      expect(result).toBe(0);
    });
  });

  describe('getPointsLogs', () => {
    it('should get points logs with pagination', async () => {
      const filters = {
        limit: 10,
        page: 1,
        search: '',
        order: 'asc' as const,
        all: false,
      };
      const mockLogs = [
        { id: 'log-1', student_id: 'student-1', points: 10, action: 'quiz' },
        { id: 'log-2', student_id: 'student-2', points: 5, action: 'post' },
      ];
      const mockCount = [{ count: 2 }];

      // Create a more sophisticated mock that handles the exact query chain
      // The actual query has: select().from().leftJoin().leftJoin().where().orderBy().limit().offset()
      const mockMainQuery = {
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(), // First leftJoin
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockLogs),
      };

      // The count query has: select().from().leftJoin().leftJoin().where()
      const mockCountQuery = {
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(), // First leftJoin
        where: jest.fn().mockResolvedValue(mockCount),
      };

      // Mock the transaction to use the same db object
      mockDrizzleService.db.transaction.mockImplementation((callback: any) =>
        callback(mockDrizzleService.db),
      );

      // Setup the select mock to return different query objects for each call
      // The Promise.all calls select twice: once for count, once for data
      mockDrizzleService.db.select
        .mockReturnValueOnce(mockMainQuery) // First call for main data query
        .mockReturnValueOnce(mockCountQuery); // Second call for count query

      const result = await repository.getPointsLogs(filters);

      expect(result).toBeDefined();
      expect(result.data).toEqual(mockLogs);
      expect(result.total).toBe(2);
    });

    it('should handle student filter', async () => {
      const filters = {
        studentId: 'student-1',
        limit: 10,
        page: 1,
        search: '',
        order: 'asc' as const,
        all: false,
      };
      const mockLogs = [{ id: 'log-1', student_id: 'student-1', points: 10 }];
      const mockCount = [{ count: 1 }];

      // Create a more sophisticated mock that handles the exact query chain
      const mockMainQuery = {
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(), // First leftJoin
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockLogs),
      };

      const mockCountQuery = {
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(), // First leftJoin
        where: jest.fn().mockResolvedValue(mockCount),
      };

      // Mock the transaction to use the same db object
      mockDrizzleService.db.transaction.mockImplementation((callback: any) =>
        callback(mockDrizzleService.db),
      );

      // Setup the select mock to return different query objects for each call
      mockDrizzleService.db.select
        .mockReturnValueOnce(mockMainQuery) // First call for main data query
        .mockReturnValueOnce(mockCountQuery); // Second call for count query

      const result = await repository.getPointsLogs(filters);

      expect(result.data).toEqual(mockLogs);
      expect(result.total).toBe(1);
    });
  });
});
