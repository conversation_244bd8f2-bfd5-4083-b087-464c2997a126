import { Test, TestingModule } from '@nestjs/testing';
import { PointSystemRepository } from './point_system.repository';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { CacheService } from '@app/shared/cache/cache.service';
import { RedisService } from '@app/shared/redis/redis.service';
import { Queue } from 'bullmq';
import { getQueueToken } from '@nestjs/bullmq';
import { NotFoundException, BadRequestException } from '@nestjs/common';

describe('PointSystemRepository', () => {
  let repository: PointSystemRepository;
  let mockDrizzleService: any;
  let mockLeaderboardQueue: any;

  beforeEach(async () => {
    mockDrizzleService = {
      db: {
        select: jest.fn(),
        insert: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
        transaction: jest.fn(),
      },
    };

    mockLeaderboardQueue = {
      add: jest.fn(),
    };

    const mockCacheService = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
    };

    const mockRedisService = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
    };

    const mockGeneralQueue = {
      add: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PointSystemRepository,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
        {
          provide: RedisService,
          useValue: mockRedisService,
        },
        {
          provide: getQueueToken('leaderboard'),
          useValue: mockLeaderboardQueue,
        },
        {
          provide: getQueueToken('general'),
          useValue: mockGeneralQueue,
        },
      ],
    }).compile();

    repository = module.get<PointSystemRepository>(PointSystemRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createPointRule', () => {
    it('should create point rule successfully', async () => {
      const ruleData = {
        action: 'quiz_completion',
        points: 10,
        description: 'Points for completing a quiz',
      };
      const mockResult = [{ id: 'rule-1', ...ruleData }];

      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      const result = await repository.createPointRule(ruleData);

      expect(mockDrizzleService.db.insert).toHaveBeenCalled();
      expect(mockInsertQuery.values).toHaveBeenCalledWith(ruleData);
      expect(mockInsertQuery.returning).toHaveBeenCalled();
      expect(mockInsertQuery.execute).toHaveBeenCalled();
      expect(result).toEqual(mockResult[0]); // The method returns result[0]
    });

    it('should handle database errors during creation', async () => {
      const ruleData = { action: 'test_action', points: 5 };
      const mockError = new Error('Database constraint violation');

      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockRejectedValue(mockError),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      await expect(repository.createPointRule(ruleData)).rejects.toThrow(
        'Database constraint violation',
      );
    });
  });

  describe('getPointRules', () => {
    it('should get point rules with default parameters', async () => {
      const mockRules = [
        { id: 'rule-1', action: 'quiz_completion', points: 10 },
        { id: 'rule-2', action: 'post_creation', points: 5 },
      ];
      const mockCount = [{ count: 2 }];

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockRules),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      // Mock count query
      const mockCountQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue(mockCount),
      };
      mockDrizzleService.db.select.mockReturnValueOnce(mockCountQuery);

      const result = await repository.getPointRules({});

      expect(result).toBeDefined();
      expect(result.data).toEqual(mockRules);
      expect(result.total).toBe(2);
    });

    it('should handle search filter', async () => {
      const filters = { search: 'quiz', limit: 10, page: 1 };
      const mockRules = [
        { id: 'rule-1', action: 'quiz_completion', points: 10 },
      ];

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockRules),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const mockCountQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue([{ count: 1 }]),
      };
      mockDrizzleService.db.select.mockReturnValueOnce(mockCountQuery);

      const result = await repository.getPointRules(filters);

      expect(result.data).toEqual(mockRules);
      expect(result.total).toBe(1);
    });
  });

  describe('updatePointRule', () => {
    it('should update point rule successfully', async () => {
      const ruleId = 'rule-1';
      const updateData = { points: 15 };
      const mockResult = [{ id: ruleId, ...updateData }];

      const mockUpdateQuery = {
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.update.mockReturnValue(mockUpdateQuery);

      const result = await repository.updatePointRule(ruleId, updateData);

      expect(mockDrizzleService.db.update).toHaveBeenCalled();
      expect(mockUpdateQuery.set).toHaveBeenCalledWith(updateData);
      expect(mockUpdateQuery.where).toHaveBeenCalled();
      expect(mockUpdateQuery.returning).toHaveBeenCalled();
      expect(result).toEqual(mockResult);
    });

    it('should throw NotFoundException when rule not found', async () => {
      const ruleId = 'non-existent';
      const updateData = { points: 20 };

      const mockUpdateQuery = {
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue([]),
      };
      mockDrizzleService.db.update.mockReturnValue(mockUpdateQuery);

      await expect(
        repository.updatePointRule(ruleId, updateData),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('deletePointRule', () => {
    it('should delete point rule successfully', async () => {
      const ruleId = 'rule-1';
      const mockResult = [{ id: ruleId }];

      const mockDeleteQuery = {
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ rowCount: 1 }),
      };
      mockDrizzleService.db.delete.mockReturnValue(mockDeleteQuery);

      const result = await repository.deletePointRule(ruleId);

      expect(mockDrizzleService.db.delete).toHaveBeenCalled();
      expect(mockDeleteQuery.where).toHaveBeenCalled();
      expect(result).toEqual({ message: 'Point rule deleted successfully' });
    });

    it('should throw NotFoundException when rule not found for deletion', async () => {
      const ruleId = 'non-existent';

      const mockDeleteQuery = {
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ rowCount: 0 }),
      };
      mockDrizzleService.db.delete.mockReturnValue(mockDeleteQuery);

      await expect(repository.deletePointRule(ruleId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('awardPoints', () => {
    it('should award points successfully', async () => {
      const pointData = {
        student_id: 'student-1',
        action: 'quiz_completion',
        points: 10,
        description: 'Quiz completed',
      };
      const mockResult = [{ id: 'log-1', ...pointData }];

      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      const result = await repository.awardPoints(pointData);

      expect(mockDrizzleService.db.insert).toHaveBeenCalled();
      expect(mockInsertQuery.values).toHaveBeenCalledWith(pointData);
      expect(mockInsertQuery.returning).toHaveBeenCalled();
      expect(result).toEqual(mockResult);
    });

    it('should handle errors during point awarding', async () => {
      const pointData = { student_id: 'student-1', action: 'test', points: 5 };
      const mockError = new Error('Database error');

      const mockInsertQuery = {
        values: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockRejectedValue(mockError),
      };
      mockDrizzleService.db.insert.mockReturnValue(mockInsertQuery);

      await expect(repository.awardPoints(pointData)).rejects.toThrow(
        'Database error',
      );
    });
  });

  describe('getStudentPoints', () => {
    it('should get student total points', async () => {
      const studentId = 'student-1';
      const mockResult = [{ totalPoints: 150 }];

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const result = await repository.getStudentPoints(studentId);

      expect(mockDrizzleService.db.select).toHaveBeenCalled();
      expect(mockQuery.from).toHaveBeenCalled();
      expect(mockQuery.where).toHaveBeenCalled();
      expect(result).toBe(150);
    });

    it('should return 0 when student has no points', async () => {
      const studentId = 'student-new';
      const mockResult = [{ totalPoints: null }];

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue(mockResult),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const result = await repository.getStudentPoints(studentId);

      expect(result).toBe(0);
    });
  });

  describe('getPointsLogs', () => {
    it('should get points logs with pagination', async () => {
      const filters = { limit: 10, page: 1 };
      const mockLogs = [
        { id: 'log-1', student_id: 'student-1', points: 10, action: 'quiz' },
        { id: 'log-2', student_id: 'student-2', points: 5, action: 'post' },
      ];

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockLogs),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const mockCountQuery = {
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue([{ count: 2 }]),
      };
      mockDrizzleService.db.select.mockReturnValueOnce(mockCountQuery);

      const result = await repository.getPointsLogs(filters);

      expect(result).toBeDefined();
      expect(result.data).toEqual(mockLogs);
      expect(result.total).toBe(2);
    });

    it('should handle student filter', async () => {
      const filters = { studentId: 'student-1', limit: 10, page: 1 };
      const mockLogs = [{ id: 'log-1', student_id: 'student-1', points: 10 }];

      const mockQuery = {
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockLogs),
      };
      mockDrizzleService.db.select.mockReturnValue(mockQuery);

      const mockCountQuery = {
        from: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue([{ count: 1 }]),
      };
      mockDrizzleService.db.select.mockReturnValueOnce(mockCountQuery);

      const result = await repository.getPointsLogs(filters);

      expect(result.data).toEqual(mockLogs);
      expect(result.total).toBe(1);
    });
  });
});
