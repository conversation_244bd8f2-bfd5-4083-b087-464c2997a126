import 'dotenv/config';
import { defineConfig } from 'drizzle-kit';

let dbUrl = process.env.DATABASE_URL;
if (!dbUrl) {
  throw new Error('Environment variable DATABASE_URL is required');
}

/*
Communication between the web server and the database is private and does not go through the public internet (Stays within the VPC).
Hence there's no need to use SSL in production. However the option is available if needed especially for the development and staging environment.
*/

dbUrl += '?sslmode=no-verify';

const sslConfig = {
  rejectUnauthorized: false,
};

export default defineConfig({
  dialect: 'postgresql',
  out: './migrations',
  schema: './src/db/schema/*.ts',
  verbose: false,
  dbCredentials: {
    url: dbUrl,
    ssl: sslConfig,
  },
  strict: true,
});
