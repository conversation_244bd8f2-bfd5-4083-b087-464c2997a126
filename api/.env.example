# Application name
APP_NAME="Touching Student Lives"

# Database configuration
DATABASE_URL==**************************************/database_name

# Node environment
NODE_ENV=  development | production | staging | local

# Server port
PORT=

# Email configuration
MAIL_EMAIL=
EMAIL_TOKEN=
EMAIL_HOST=
EMAIL_PORT=

# Backend and frontend URLs
BACKEND_URL=
FRONTEND_URL=

# Token configurations
REFRESH_TOKEN_EXPIRY=
REFRESH_TOKEN_SECRET=
ACCESS_TOKEN_EXPIRY=
ACCESS_TOKEN_SECRET=
MAGIC_LINK_EXPIRY=


# Email toggle (ON for production, OFF for staging/development)
EMAIL_TOGGLE=ON

# OTP hash secret
OTP_HASH_SECRET=
# AUTH DURATION
OTP_EXPIRY_TIME=
# AUTH DURATION

# OTP hash secret
SENTRY_DSN=


# SUPER ADMIN CONFIGURATION
SUPER_ADMIN_EMAIL=
PASSWORD=******
ROLE=
STATE=

#  AWS S3 CONFIGURATION VARIABLE
AWS_REGION=eu-central-1
AWS_ACCESS_KEY_ID=*******
AWS_SECRET_ACCESS_KEY=*******
AWS_ENDPOINT=XXXXXXXXXXXXXXXXXXXXX
AWS_BUCKET_NAME=bucket_name
MAX_FILE_SIZE=111

# Redis configuration for BullMQ
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_USERNAME=
REDIS_DB=0
REDIS_TLS=false
REDIS_MODE=single
REDIS_LOG_LEVEL=info
REDIS_CONNECTION_TIMEOUT_MS=5000
REDIS_COMMAND_TIMEOUT_MS=5000
REDIS_HEALTHCHECK_INTERVAL_MS=30000



# BullMQ configuration
QUEUE_CONCURRENCY=5
QUEUE_ATTEMPTS=3
QUEUE_BACKOFF_DELAY=1000
QUEUE_REMOVE_ON_COMPLETE=true
QUEUE_REMOVE_ON_FAIL=false

# Bull Board UI configuration
BULL_BOARD_USERNAME=admin
BULL_BOARD_PASSWORD=admin

# Cache configuration
CACHE_ENABLED=true
CACHE_VERSION=1
CACHE_NAMESPACE=app
CACHE_DEFAULT_TTL=3600
CACHE_WARMUP_ENABLED=true