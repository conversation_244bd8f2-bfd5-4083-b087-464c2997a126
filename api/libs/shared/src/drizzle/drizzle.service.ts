import {
  Inject,
  Injectable,
  Logger,
  OnM<PERSON>ule<PERSON><PERSON>roy,
  OnModuleInit,
} from '@nestjs/common';
import { Pool } from 'pg';
import { drizzle, NodePgDatabase } from 'drizzle-orm/node-postgres';
import * as databaseSchema from 'src/db/schema/index';
import { CONNECTION_POOL } from './drizzle.module-definition';

@Injectable()
export class DrizzleService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(DrizzleService.name);
  public db: NodePgDatabase<typeof databaseSchema>;
  private isPoolHealthy = false;
  private connectionAttempts = 0;
  private readonly MAX_CONNECTION_ATTEMPTS = 5;
  private readonly RETRY_DELAY_MS = 2000;
  private healthCheckInterval: NodeJS.Timeout | null = null;

  constructor(@Inject(CONNECTION_POOL) private readonly pool: Pool) {
    this.db = drizzle(this.pool, {
      schema: databaseSchema,
      logger: false,
    });
    this.setupEventListeners();
  }

  private setupEventListeners() {
    this.pool.on('error', (err: any) => {
      this.isPoolHealthy = false;
      // Only log essential error information, omit stack trace in non-development environments
      this.logger.error('Database pool error', {
        error: {
          message: err.message,
          code: err.code,
          errno: err.errno,
          syscall: err.syscall,
          ...(process.env.NODE_ENV === 'development' && { stack: err.stack }),
        },
      });
    });

    this.pool.on('connect', () => {
      this.isPoolHealthy = true;
    });

    this.pool.on('remove', () => {
      // Client removal logging disabled for cleaner logs
    });
  }

  async onModuleInit() {
    try {
      await this.initializePool();
      this.startHealthCheckInterval();
    } catch (error) {
      // Don't throw the error to prevent application startup failure
      // The pool will handle reconnections automatically
      this.logger.warn(
        'Database pool not available during application startup - will retry in background',
        {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      );
    }
  }

  private async initializePool(): Promise<void> {
    this.connectionAttempts++;

    try {
      // Only log first attempt to reduce noise during startup
      if (this.connectionAttempts === 1) {
        this.logger.log(
          `Initializing database pool (attempt ${this.connectionAttempts}/${this.MAX_CONNECTION_ATTEMPTS})...`,
        );
      }

      // Test the pool with a simple query
      const client = await this.pool.connect();
      try {
        await client.query('SELECT 1');
        this.isPoolHealthy = true;
        this.connectionAttempts = 0;
        this.logger.log('Database pool initialized successfully');

        // Database pool test logging disabled for cleaner logs
      } finally {
        client.release();
      }
    } catch (error: any) {
      this.isPoolHealthy = false;

      // Only log detailed error information on first attempt or final attempt
      if (
        this.connectionAttempts === 1 ||
        this.connectionAttempts === this.MAX_CONNECTION_ATTEMPTS
      ) {
        const formattedError = {
          message: error.message,
          code: error.code,
          errno: error.errno,
          syscall: error.syscall,
          // Only include these in development
          ...(process.env.NODE_ENV === 'development' && {
            host: error.address,
            port: error.port,
            stack: error.stack,
          }),
        };

        this.logger.error(
          `Failed to initialize database pool (attempt ${this.connectionAttempts}/${this.MAX_CONNECTION_ATTEMPTS})`,
          formattedError,
        );
      }

      // If we haven't reached the maximum number of attempts, retry
      if (this.connectionAttempts < this.MAX_CONNECTION_ATTEMPTS) {
        // Only log retry message on first attempt
        if (this.connectionAttempts === 1) {
          this.logger.log(
            `Retrying database pool initialization in ${this.RETRY_DELAY_MS}ms...`,
          );
        }

        // Wait before retrying with exponential backoff
        const delay = Math.min(
          this.RETRY_DELAY_MS * this.connectionAttempts,
          10000,
        );
        await new Promise((resolve) => setTimeout(resolve, delay));

        // Retry connection
        return this.initializePool();
      } else {
        this.logger.error(
          `Maximum database pool initialization attempts (${this.MAX_CONNECTION_ATTEMPTS}) reached. Giving up.`,
        );
        throw new Error(
          `Failed to initialize database pool after ${this.MAX_CONNECTION_ATTEMPTS} attempts: ${error.message}`,
        );
      }
    }
  }

  private startHealthCheckInterval(): void {
    // Start periodic health checks every 30 seconds
    this.healthCheckInterval = setInterval(async () => {
      try {
        await this.isHealthy();
      } catch (error) {
        // Health check errors are already logged in isHealthy method
      }
    }, 30000);
  }

  async onModuleDestroy() {
    try {
      // Clear health check interval
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        this.healthCheckInterval = null;
      }

      this.logger.log('Closing database pool...');
      await this.pool.end();
      // Database pool closure logging disabled for cleaner logs
    } catch (error: any) {
      this.logger.error('Error closing database pool', {
        error: {
          message: error.message,
          ...(process.env.NODE_ENV === 'development' && { stack: error.stack }),
        },
      });
    }
  }

  async close() {
    try {
      // Clear health check interval
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        this.healthCheckInterval = null;
      }

      // Database pool closure logging disabled for cleaner logs
      await this.pool.end();
      return true;
    } catch (error: any) {
      this.logger.error('Error closing database pool', {
        message: error.message,
      });
      return false;
    }
  }

  async isHealthy(): Promise<boolean> {
    try {
      // Try to get a client from the pool and execute a simple query
      const client = await this.pool.connect();
      try {
        const result = await client.query('SELECT 1');
        this.isPoolHealthy = true;
        return result.rows.length > 0;
      } finally {
        client.release();
      }
    } catch (error: any) {
      this.isPoolHealthy = false;
      // Only log detailed error in development
      if (process.env.NODE_ENV === 'development') {
        this.logger.error('Database health check failed', error);
      } else {
        this.logger.error('Database health check failed');
      }
      return false;
    }
  }

  /**
   * Ensures database connection is available, attempts reconnection if needed
   */
  async ensureConnection(): Promise<void> {
    if (this.isPoolHealthy) {
      return;
    }

    try {
      // Reset connection attempts for background reconnection
      this.connectionAttempts = 0;
      await this.initializePool();
    } catch (error) {
      // Log but don't throw to allow graceful degradation
      this.logger.warn('Failed to establish database pool connection', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Get comprehensive pool metrics for monitoring
   */
  getPoolMetrics() {
    // Access pool configuration through the pool instance
    const poolConfig = (this.pool as any).options || {};
    return {
      totalCount: this.pool.totalCount,
      idleCount: this.pool.idleCount,
      waitingCount: this.pool.waitingCount,
      isPoolHealthy: this.isPoolHealthy,
      connectionAttempts: this.connectionAttempts,
      maxConnections: poolConfig.max || 20,
      minConnections: poolConfig.min || 1,
    };
  }

  /**
   * Get pool status for health checks
   */
  getPoolStatus() {
    const metrics = this.getPoolMetrics();
    return {
      healthy: this.isPoolHealthy,
      metrics,
      utilizationPercentage: Math.round(
        (metrics.totalCount / metrics.maxConnections) * 100,
      ),
      availableConnections: metrics.maxConnections - metrics.totalCount,
    };
  }
}

export type DatabaseType = NodePgDatabase<typeof databaseSchema>;
