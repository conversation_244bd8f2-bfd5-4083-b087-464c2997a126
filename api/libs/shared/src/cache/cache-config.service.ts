import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class CacheConfigService {
  private readonly DEFAULT_NAMESPACE = 'app';
  private readonly DEFAULT_VERSION = '1';

  constructor(private readonly configService: ConfigService) {}

  /**
   * Gets the cache namespace
   */
  getNamespace(): string {
    return (
      this.configService.get<string>('CACHE_NAMESPACE') ??
      this.DEFAULT_NAMESPACE
    );
  }

  /**
   * Gets the current cache version
   * Incrementing this version will effectively invalidate all existing caches
   */
  getCacheVersion(): string {
    return (
      this.configService.get<string>('CACHE_VERSION') ?? this.DEFAULT_VERSION
    );
  }

  /**
   * Checks if caching is enabled
   */
  isCacheEnabled(): boolean {
    return this.configService.get<string>('CACHE_ENABLED') !== 'false';
  }

  /**
   * Gets the default TTL for cache entries
   */
  getDefaultTTL(): number {
    return parseInt(
      this.configService.get<string>('CACHE_DEFAULT_TTL') ?? '3600',
      10,
    );
  }
}
