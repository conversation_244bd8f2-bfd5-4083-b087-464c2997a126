import { Module } from '@nestjs/common';
import { EnhancedNotificationService } from './enhanced-notification.service';
import { EnhancedNotificationController } from './enhanced-notification.controller';

import { FirebaseModule } from '@/firebase/firebase.module';
import { EmailModule } from '@/mail/email.module';
import { NotificationModule } from '../notification/notification.module';
import { BullModule } from '@nestjs/bullmq';
import { CacheModule } from '@app/shared/cache/cache.module';
import { ConfigurableDatabaseModule } from '@app/shared/drizzle/drizzle.module-definition';
import { NotificationTemplateService } from '../notification/notification-template.service';
import { NotificationTypeService } from '../notification/notification-type.service';
import { NotificationPreferenceService } from './notification-preference.service';
import { NotificationHistoryService } from '../notification/notification-history.service';
import { ScheduledNotificationService } from './scheduled-notification.service';
import { DeviceTokenService } from './device-token.service';
import { NotificationChannelService } from './notification-channel.service';
import { QueueName } from '@app/shared/queue/queue.constants';
import { EnhancedNotificationQueueModule } from '../enhanced-notification-queue/enhanced-notification-queue.module';
import { UserRepository } from '@/repositories/user.repository';
import { NotificationEntityService } from '../notification/notification-entity.service';
import { QueueService } from '../queue/queue.service';

@Module({
  imports: [
    FirebaseModule,
    EmailModule,
    NotificationModule,
    EnhancedNotificationQueueModule,
    BullModule.registerQueue(
      {
        name: QueueName.NOTIFICATION,
      },
      {
        name: QueueName.EMAIL,
      },
      {
        name: QueueName.IN_APP,
      },
      {
        name: QueueName.PUSH,
      },
      {
        name: QueueName.GENERAL,
      },
    ),
    CacheModule,
    ConfigurableDatabaseModule.register({
      connectionString:
        process.env.DATABASE_URL ??
        'postgresql://postgres:postgres@localhost:5432/postgres',
    }),
  ],
  controllers: [EnhancedNotificationController],
  providers: [
    EnhancedNotificationService,

    NotificationTemplateService,
    NotificationTypeService,
    NotificationPreferenceService,
    NotificationHistoryService,
    ScheduledNotificationService,
    DeviceTokenService,
    NotificationChannelService,
    NotificationEntityService,
    UserRepository,
    QueueService,
  ],
  exports: [
    EnhancedNotificationService,
    NotificationTemplateService,
    NotificationTypeService,
    NotificationPreferenceService,
    NotificationHistoryService,
    ScheduledNotificationService,
    DeviceTokenService,
    NotificationChannelService,
    NotificationEntityService,
  ],
})
export class EnhancedNotificationModule {}
