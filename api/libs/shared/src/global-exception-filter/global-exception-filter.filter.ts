import type { ArgumentsHost } from '@nestjs/common';
import { HttpException, HttpStatus } from '@nestjs/common';
import { Catch } from '@nestjs/common';
import { BaseExceptionFilter } from '@nestjs/core';

import type { Response } from 'express';
import { ZodValidationException } from 'nestjs-zod';
import { JsonWebTokenError } from 'jsonwebtoken';
import { statusMessage } from '../constants/error-status.constant';
import { DatabaseError } from 'pg';
import {
  Foreign_Key_Violation_Error_Code,
  Unique_Constraint_Error_Code,
} from '../constants/database.constants';
import { formattedTableName } from '../utils/databaseUtils';

@Catch()
export class GlobalExceptionFilterFilter extends BaseExceptionFilter {
  override catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    if (exception instanceof ZodValidationException) {
      response.status(exception.getStatus()).json({
        message: exception.message,
        data: exception.getZodError().errors.map(({ path, message }) => {
          return { path, message };
        }),
      });
      return;
    }

    if (exception instanceof JsonWebTokenError) {
      response.status(HttpStatus.UNAUTHORIZED).json({
        status: HttpStatus.UNAUTHORIZED,
        message: statusMessage.Unauthorized,
      });
      return;
    }

    if (exception instanceof DatabaseError) {
      if (exception.code == Unique_Constraint_Error_Code) {
        const tableName = exception.table!;
        const formattedTableName =
          tableName.charAt(0).toUpperCase() +
          tableName.slice(1).toLowerCase().replace(/_/g, ' ');

        response.status(HttpStatus.CONFLICT).json({
          status: HttpStatus.CONFLICT,
          message: `${formattedTableName} Already exists`,
        });
        return;
      }
      if (exception.code == Foreign_Key_Violation_Error_Code) {
        if (exception.detail?.includes('is still referenced from table')) {
          response.status(HttpStatus.CONFLICT).json({
            status: HttpStatus.CONFLICT,
            message:
              'Deletion failed: Cannot delete the table because Other records depend on this table',
          });
        } else {
          const match = exception.detail?.match(
            /\(([^)]+)\)=\(([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})\).*?table "([^"]+)"/,
          );
          if (match) {
            const [, , id, tableName] = match;

            const referencedTable = formattedTableName(tableName!);
            response.status(HttpStatus.CONFLICT).json({
              status: HttpStatus.CONFLICT,
              message: ` ${referencedTable} with id ${id} does not exist`,
            });
          } else {
            response.status(HttpStatus.CONFLICT).json({
              status: HttpStatus.CONFLICT,
              message: 'Foreign key violation error',
            });
          }
          return;
        }
      }
    }
    if (exception instanceof HttpException) {
      const status = exception.getStatus();
      const exceptionResponse = exception.getResponse();

      response.status(status).json({
        status,
        ...(typeof exceptionResponse === 'object'
          ? exceptionResponse
          : {
              message: exception.message || exceptionResponse,
            }),
      });
    } else {
      response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        status: HttpStatus.INTERNAL_SERVER_ERROR,
        message: statusMessage.InternalServerError,
      });
    }
    super.catch(exception, host);
  }
}
