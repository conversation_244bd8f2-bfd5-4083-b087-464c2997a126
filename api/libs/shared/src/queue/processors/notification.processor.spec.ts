import { Test, TestingModule } from '@nestjs/testing';
import { NotificationProcessor } from './notification.processor';
import { EnhancedNotificationService } from '../../enhanced-notification/enhanced-notification.service';
import { Job } from 'bullmq';
import { NotificationJobType } from '../queue.constants';
import type {
  SingleNotificationJobData,
  BulkNotificationJobData,
} from '../queue.types';

describe('NotificationProcessor', () => {
  let processor: NotificationProcessor;
  let notificationService: jest.Mocked<EnhancedNotificationService>;

  const mockNotificationService = {
    sendNotificationToUser: jest.fn(),
    sendDirectNotification: jest.fn(),
    getNotificationType: jest.fn(),
    processScheduledNotifications: jest.fn(),
    getTargetUsers: jest.fn(),
    executeQuery: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationProcessor,
        {
          provide: EnhancedNotificationService,
          useValue: mockNotificationService,
        },
      ],
    }).compile();

    processor = module.get<NotificationProcessor>(NotificationProcessor);
    notificationService = module.get<EnhancedNotificationService>(
      EnhancedNotificationService,
    ) as jest.Mocked<EnhancedNotificationService>;
    jest.clearAllMocks();
  });

  describe('process', () => {
    it('should be defined', () => {
      expect(processor).toBeDefined();
    });

    it('should process single notification job successfully', async () => {
      const job = {
        id: 'test-job-1',
        name: NotificationJobType.SEND_SINGLE,
        data: {
          userId: 'user-123',
          notificationTypeId: 'type-456',
          title: 'Test Notification',
          body: 'Test notification body',
          data: { key: 'value' },
          channels: ['email', 'push'],
          overridePreferences: false,
        },
      } as Job<SingleNotificationJobData>;

      notificationService.getNotificationType.mockResolvedValue({
        id: 'type-456',
        name: 'Test Type',
        description: 'Test notification type',
      });

      notificationService.sendNotificationToUser.mockResolvedValue(undefined);

      const result = await processor.process(job);

      expect(notificationService.sendNotificationToUser).toHaveBeenCalledWith({
        userId: 'user-123',
        notificationTypeId: 'type-456',
        data: {
          key: 'value',
          title: 'Test Notification',
          body: 'Test notification body',
        },
        channels: ['email', 'push'],
        overridePreferences: false,
      });

      expect(result).toEqual({
        success: true,
        message: 'Notification sent to user user-123 (Test Type)',
        data: {
          summary:
            'User: user-123..., Type: type-456..., Channels: email, push',
          title: 'Test Notification',
          body: 'Test notification body',
        },
      });
    });

    it('should handle single notification without notification type details', async () => {
      const job = {
        id: 'test-job-2',
        name: NotificationJobType.SEND_SINGLE,
        data: {
          userId: 'user-123',
          notificationTypeId: 'type-456',
          title: 'Test Notification',
          body: 'Test notification body',
          data: { key: 'value' },
          channels: ['email'],
          overridePreferences: false,
        },
      } as Job<SingleNotificationJobData>;

      notificationService.getNotificationType.mockResolvedValue(null);
      notificationService.sendNotificationToUser.mockResolvedValue(undefined);

      const result = await processor.process(job);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Notification sent to user user-123');
    });

    it('should handle single notification job failure', async () => {
      const job = {
        id: 'test-job-3',
        name: NotificationJobType.SEND_SINGLE,
        data: {
          userId: 'user-123',
          notificationTypeId: 'type-456',
          title: 'Test Notification',
          body: 'Test notification body',
          data: { key: 'value' },
          channels: ['email'],
          overridePreferences: false,
        },
      } as Job<SingleNotificationJobData>;

      const error = new Error('Notification service error');
      notificationService.sendNotificationToUser.mockRejectedValue(error);

      const result = await processor.process(job);

      expect(result).toEqual({
        success: false,
        message: 'Failed to send notification to user user-123',
        error: 'Notification service error',
        data: {
          userId: 'user-123',
          notificationTypeId: 'type-456',
          channels: ['email'],
        },
      });
    });

    it('should process bulk notification job successfully', async () => {
      const job = {
        id: 'test-job-4',
        name: NotificationJobType.SEND_BULK,
        data: {
          notificationTypeId: 'type-456',
          data: { message: 'Bulk notification' },
          targetAudience: {
            roles: ['student'],
            filters: { active: true },
          },
          channels: ['email', 'push'],
          overridePreferences: false,
        },
      } as Job<BulkNotificationJobData>;

      const mockUsers = [
        { id: 'user-1', email: '<EMAIL>' },
        { id: 'user-2', email: '<EMAIL>' },
      ];

      notificationService.executeQuery.mockResolvedValue({ rows: mockUsers });
      notificationService.sendDirectNotification.mockResolvedValue(undefined);

      const result = await processor.process(job);

      expect(notificationService.executeQuery).toHaveBeenCalled();

      expect(notificationService.sendDirectNotification).toHaveBeenCalledTimes(
        2,
      );
      expect(result.success).toBe(true);
      expect(result.data.totalUsers).toBe(2);
      expect(result.data.successCount).toBe(2);
      expect(result.data.failureCount).toBe(0);
    });

    it('should handle bulk notification with partial failures', async () => {
      const job = {
        id: 'test-job-5',
        name: NotificationJobType.SEND_BULK,
        data: {
          notificationTypeId: 'type-456',
          data: { message: 'Bulk notification' },
          targetAudience: {
            roles: ['student'],
          },
          channels: ['email'],
          overridePreferences: false,
        },
      } as Job<BulkNotificationJobData>;

      const mockUsers = [
        { id: 'user-1', email: '<EMAIL>' },
        { id: 'user-2', email: '<EMAIL>' },
      ];

      notificationService.executeQuery.mockResolvedValue({ rows: mockUsers });
      notificationService.sendDirectNotification
        .mockResolvedValueOnce(undefined) // First succeeds
        .mockRejectedValueOnce(new Error('Second fails')); // Second fails

      const result = await processor.process(job);

      expect(result.success).toBe(true); // Bulk notifications return success: true even with partial failures
      expect(result.data.successCount).toBe(1);
      expect(result.data.failureCount).toBe(1);
      expect(result.data.totalUsers).toBe(2);
    });

    it('should process scheduled notifications job successfully', async () => {
      const job = {
        id: 'test-job-6',
        name: NotificationJobType.PROCESS_SCHEDULED,
        data: {},
      } as Job;

      const mockResult = {
        processed: 5,
        failed: 0,
        total: 5,
      };

      notificationService.processScheduledNotifications.mockResolvedValue(
        mockResult,
      );

      const result = await processor.process(job);

      expect(
        notificationService.processScheduledNotifications,
      ).toHaveBeenCalled();
      expect(result).toEqual({
        success: true,
        message: 'Scheduled notifications processed successfully',
      });
    });

    it('should handle scheduled notifications job failure', async () => {
      const job = {
        id: 'test-job-7',
        name: NotificationJobType.PROCESS_SCHEDULED,
        data: {},
      } as Job;

      const error = new Error('Scheduled processing error');
      notificationService.processScheduledNotifications.mockRejectedValue(
        error,
      );

      const result = await processor.process(job);

      expect(result).toEqual({
        success: false,
        message: 'Failed to process scheduled notifications',
        error: 'Scheduled processing error',
      });
    });

    it('should throw error for unknown job type', async () => {
      const job = {
        id: 'test-job-8',
        name: 'UNKNOWN_TYPE',
        data: {},
      } as Job;

      await expect(processor.process(job)).rejects.toThrow(
        'Unknown job type: UNKNOWN_TYPE',
      );
    });

    it('should handle job processing errors', async () => {
      const job = {
        id: 'test-job-9',
        name: NotificationJobType.SEND_SINGLE,
        data: {
          userId: 'user-123',
          notificationTypeId: 'type-456',
          channels: ['email'],
        },
      } as Job<SingleNotificationJobData>;

      // Mock a processing error
      notificationService.sendNotificationToUser.mockImplementation(() => {
        throw new Error('Processing error');
      });

      const result = await processor.process(job);

      expect(result).toEqual({
        success: false,
        message: 'Failed to send notification to user user-123',
        error: 'Processing error',
        data: {
          userId: 'user-123',
          notificationTypeId: 'type-456',
          channels: ['email'],
        },
      });
    });

    it('should handle bulk notification with no target users', async () => {
      const job = {
        id: 'test-job-10',
        name: NotificationJobType.SEND_BULK,
        data: {
          notificationTypeId: 'type-456',
          data: { message: 'Bulk notification' },
          targetAudience: {
            roles: ['nonexistent'],
          },
          channels: ['email'],
        },
      } as Job<BulkNotificationJobData>;

      // Mock empty result for all queries (main, fallback, and final fallback)
      notificationService.executeQuery
        .mockResolvedValueOnce({ rows: [] }) // Main query
        .mockResolvedValueOnce({ rows: [] }) // Fallback query
        .mockResolvedValueOnce({ rows: [] }); // Final fallback query

      const result = await processor.process(job);

      expect(result.success).toBe(true);
      expect(result.message).toBe(
        'No users found matching the target audience',
      );
    });
  });

  describe('Fallback Query Logic', () => {
    it('should use fallback query when main query returns no users', async () => {
      const job = {
        id: 'test-job-fallback',
        name: NotificationJobType.SEND_BULK,
        data: {
          notificationTypeId: 'type-123',
          targetAudience: {
            filters: {
              postId: 'post-123',
              clubId: 'club-456',
            },
            roles: ['student'],
          },
          channels: ['email'],
          content: {
            title: 'Test Notification',
            message: 'Test message',
          },
        },
      } as Job<BulkNotificationJobData>;

      // Mock main query to return empty result
      // Mock fallback query to return users
      // Mock final fallback query to return users
      notificationService.executeQuery
        .mockResolvedValueOnce({ rows: [] }) // Main query - empty
        .mockResolvedValueOnce({
          rows: [
            {
              id: 'user-1',
              email: '<EMAIL>',
              role: 'student',
              state: 'active',
            },
          ],
        }) // Fallback query - has users
        .mockResolvedValueOnce({ rows: [] }); // Final fallback query - not called

      notificationService.sendNotificationToUser.mockResolvedValue(undefined);

      const result = await processor.process(job);

      expect(result.success).toBe(true);
      expect(result.data.totalUsers).toBe(1);
      expect(result.data.successCount).toBe(1);
      expect(result.data.failureCount).toBe(0);
      expect(notificationService.executeQuery).toHaveBeenCalledTimes(2); // Main + fallback
    });

    it('should use final fallback query when both main and fallback queries return no users', async () => {
      const job = {
        id: 'test-job-final-fallback',
        name: NotificationJobType.SEND_BULK,
        data: {
          notificationTypeId: 'type-123',
          targetAudience: {
            filters: {
              postId: 'post-123',
              clubId: 'club-456',
            },
            roles: ['student'],
          },
          channels: ['email'],
          content: {
            title: 'Test Notification',
            message: 'Test message',
          },
        },
      } as Job<BulkNotificationJobData>;

      // Mock all queries to return empty results except final fallback
      notificationService.executeQuery
        .mockResolvedValueOnce({ rows: [] }) // Main query - empty
        .mockResolvedValueOnce({ rows: [] }) // Fallback query - empty
        .mockResolvedValueOnce({
          rows: [
            {
              id: 'user-1',
              email: '<EMAIL>',
              role: 'student',
              state: 'active',
            },
          ],
        }); // Final fallback query - has users

      notificationService.sendNotificationToUser.mockResolvedValue(undefined);

      const result = await processor.process(job);

      expect(result.success).toBe(true);
      expect(result.data.totalUsers).toBe(1);
      expect(result.data.successCount).toBe(1);
      expect(result.data.failureCount).toBe(0);
      expect(notificationService.executeQuery).toHaveBeenCalledTimes(3); // Main + fallback + final fallback
    });

    it('should handle bulk notification processing errors', async () => {
      const job = {
        id: 'test-job-error',
        name: NotificationJobType.SEND_BULK,
        data: {
          notificationTypeId: 'type-123',
          targetAudience: {
            filters: {},
            roles: ['student'],
          },
          channels: ['email'],
          content: {
            title: 'Test Notification',
            message: 'Test message',
          },
        },
      } as Job<BulkNotificationJobData>;

      // Mock executeQuery to throw an error
      notificationService.executeQuery.mockRejectedValue(
        new Error('Database connection failed'),
      );

      const result = await processor.process(job);

      expect(result.success).toBe(false);
      expect(result.message).toBe('Failed to send bulk notifications');
      expect(result.error).toBe('Database connection failed');
      expect(result.data).toEqual({
        notificationTypeId: 'type-123',
        targetAudience: {
          filters: {},
          roles: ['student'],
        },
        channels: ['email'],
      });
    });
  });

  describe('Scheduled Notifications', () => {
    it('should process scheduled notifications successfully', async () => {
      const job = {
        id: 'test-job-scheduled',
        name: NotificationJobType.PROCESS_SCHEDULED,
        data: {},
      } as Job<any>;

      notificationService.processScheduledNotifications.mockResolvedValue(
        undefined,
      );

      const result = await processor.process(job);

      expect(result.success).toBe(true);
      expect(result.message).toBe(
        'Scheduled notifications processed successfully',
      );
      expect(
        notificationService.processScheduledNotifications,
      ).toHaveBeenCalled();
    });

    it('should handle scheduled notification processing errors', async () => {
      const job = {
        id: 'test-job-scheduled-error',
        name: NotificationJobType.PROCESS_SCHEDULED,
        data: {},
      } as Job<any>;

      notificationService.processScheduledNotifications.mockRejectedValue(
        new Error('Scheduled processing failed'),
      );

      const result = await processor.process(job);

      expect(result.success).toBe(false);
      expect(result.message).toBe('Failed to process scheduled notifications');
      expect(result.error).toBe('Scheduled processing failed');
    });
  });
});
