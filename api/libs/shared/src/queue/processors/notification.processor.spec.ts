import { Test, TestingModule } from '@nestjs/testing';
import { NotificationProcessor } from './notification.processor';
import { EnhancedNotificationService } from '../../enhanced-notification/enhanced-notification.service';
import { Job } from 'bullmq';
import { NotificationJobType } from '../queue.constants';
import type { SingleNotificationJobData, BulkNotificationJobData } from '../queue.types';

describe('NotificationProcessor', () => {
  let processor: NotificationProcessor;
  let notificationService: jest.Mocked<EnhancedNotificationService>;

  const mockNotificationService = {
    sendNotificationToUser: jest.fn(),
    sendDirectNotification: jest.fn(),
    getNotificationType: jest.fn(),
    processScheduledNotifications: jest.fn(),
    getTargetUsers: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationProcessor,
        {
          provide: EnhancedNotificationService,
          useValue: mockNotificationService,
        },
      ],
    }).compile();

    processor = module.get<NotificationProcessor>(NotificationProcessor);
    notificationService = module.get<EnhancedNotificationService>(
      EnhancedNotificationService,
    ) as jest.Mocked<EnhancedNotificationService>;
    jest.clearAllMocks();
  });

  describe('process', () => {
    it('should be defined', () => {
      expect(processor).toBeDefined();
    });

    it('should process single notification job successfully', async () => {
      const job = {
        id: 'test-job-1',
        name: NotificationJobType.SEND_SINGLE,
        data: {
          userId: 'user-123',
          notificationTypeId: 'type-456',
          title: 'Test Notification',
          body: 'Test notification body',
          data: { key: 'value' },
          channels: ['email', 'push'],
          overridePreferences: false,
        },
      } as Job<SingleNotificationJobData>;

      notificationService.getNotificationType.mockResolvedValue({
        id: 'type-456',
        name: 'Test Type',
        description: 'Test notification type',
      });

      notificationService.sendNotificationToUser.mockResolvedValue(undefined);

      const result = await processor.process(job);

      expect(notificationService.sendNotificationToUser).toHaveBeenCalledWith({
        userId: 'user-123',
        notificationTypeId: 'type-456',
        data: {
          key: 'value',
          title: 'Test Notification',
          body: 'Test notification body',
        },
        channels: ['email', 'push'],
        overridePreferences: false,
      });

      expect(result).toEqual({
        success: true,
        message: 'Notification sent to user user-123 (Test Type)',
        data: {
          userId: 'user-123',
          notificationTypeId: 'type-456',
          channels: ['email', 'push'],
        },
      });
    });

    it('should handle single notification without notification type details', async () => {
      const job = {
        id: 'test-job-2',
        name: NotificationJobType.SEND_SINGLE,
        data: {
          userId: 'user-123',
          notificationTypeId: 'type-456',
          title: 'Test Notification',
          body: 'Test notification body',
          data: { key: 'value' },
          channels: ['email'],
          overridePreferences: false,
        },
      } as Job<SingleNotificationJobData>;

      notificationService.getNotificationType.mockResolvedValue(null);
      notificationService.sendNotificationToUser.mockResolvedValue(undefined);

      const result = await processor.process(job);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Notification sent to user user-123');
    });

    it('should handle single notification job failure', async () => {
      const job = {
        id: 'test-job-3',
        name: NotificationJobType.SEND_SINGLE,
        data: {
          userId: 'user-123',
          notificationTypeId: 'type-456',
          title: 'Test Notification',
          body: 'Test notification body',
          data: { key: 'value' },
          channels: ['email'],
          overridePreferences: false,
        },
      } as Job<SingleNotificationJobData>;

      const error = new Error('Notification service error');
      notificationService.sendNotificationToUser.mockRejectedValue(error);

      const result = await processor.process(job);

      expect(result).toEqual({
        success: false,
        message: 'Failed to send notification to user user-123',
        error: 'Notification service error',
        data: {
          userId: 'user-123',
          notificationTypeId: 'type-456',
          channels: ['email'],
        },
      });
    });

    it('should process bulk notification job successfully', async () => {
      const job = {
        id: 'test-job-4',
        name: NotificationJobType.SEND_BULK,
        data: {
          notificationTypeId: 'type-456',
          data: { message: 'Bulk notification' },
          targetAudience: {
            roles: ['student'],
            filters: { active: true },
          },
          channels: ['email', 'push'],
          overridePreferences: false,
        },
      } as Job<BulkNotificationJobData>;

      const mockUsers = [
        { id: 'user-1', email: '<EMAIL>' },
        { id: 'user-2', email: '<EMAIL>' },
      ];

      notificationService.getTargetUsers.mockResolvedValue(mockUsers);
      notificationService.sendDirectNotification.mockResolvedValue(undefined);

      const result = await processor.process(job);

      expect(notificationService.getTargetUsers).toHaveBeenCalledWith({
        roles: ['student'],
        filters: { active: true },
      });

      expect(notificationService.sendDirectNotification).toHaveBeenCalledTimes(2);
      expect(result.success).toBe(true);
      expect(result.data.totalUsers).toBe(2);
      expect(result.data.successCount).toBe(2);
      expect(result.data.failureCount).toBe(0);
    });

    it('should handle bulk notification with partial failures', async () => {
      const job = {
        id: 'test-job-5',
        name: NotificationJobType.SEND_BULK,
        data: {
          notificationTypeId: 'type-456',
          data: { message: 'Bulk notification' },
          targetAudience: {
            roles: ['student'],
          },
          channels: ['email'],
          overridePreferences: false,
        },
      } as Job<BulkNotificationJobData>;

      const mockUsers = [
        { id: 'user-1', email: '<EMAIL>' },
        { id: 'user-2', email: '<EMAIL>' },
      ];

      notificationService.getTargetUsers.mockResolvedValue(mockUsers);
      notificationService.sendDirectNotification
        .mockResolvedValueOnce(undefined) // First succeeds
        .mockRejectedValueOnce(new Error('Second fails')); // Second fails

      const result = await processor.process(job);

      expect(result.success).toBe(true);
      expect(result.data.successCount).toBe(1);
      expect(result.data.failureCount).toBe(1);
      expect(result.data.failures).toHaveLength(1);
    });

    it('should process scheduled notifications job successfully', async () => {
      const job = {
        id: 'test-job-6',
        name: NotificationJobType.PROCESS_SCHEDULED,
        data: {},
      } as Job;

      const mockResult = {
        processed: 5,
        failed: 0,
        total: 5,
      };

      notificationService.processScheduledNotifications.mockResolvedValue(mockResult);

      const result = await processor.process(job);

      expect(notificationService.processScheduledNotifications).toHaveBeenCalled();
      expect(result).toEqual({
        success: true,
        message: 'Processed 5 scheduled notifications',
        data: mockResult,
      });
    });

    it('should handle scheduled notifications job failure', async () => {
      const job = {
        id: 'test-job-7',
        name: NotificationJobType.PROCESS_SCHEDULED,
        data: {},
      } as Job;

      const error = new Error('Scheduled processing error');
      notificationService.processScheduledNotifications.mockRejectedValue(error);

      const result = await processor.process(job);

      expect(result).toEqual({
        success: false,
        message: 'Failed to process scheduled notifications',
        error: 'Scheduled processing error',
      });
    });

    it('should throw error for unknown job type', async () => {
      const job = {
        id: 'test-job-8',
        name: 'UNKNOWN_TYPE',
        data: {},
      } as Job;

      await expect(processor.process(job)).rejects.toThrow('Unknown job type: UNKNOWN_TYPE');
    });

    it('should handle job processing errors', async () => {
      const job = {
        id: 'test-job-9',
        name: NotificationJobType.SEND_SINGLE,
        data: {
          userId: 'user-123',
          notificationTypeId: 'type-456',
          channels: ['email'],
        },
      } as Job<SingleNotificationJobData>;

      // Mock a processing error
      notificationService.sendNotificationToUser.mockImplementation(() => {
        throw new Error('Processing error');
      });

      await expect(processor.process(job)).rejects.toThrow('Processing error');
    });

    it('should handle bulk notification with no target users', async () => {
      const job = {
        id: 'test-job-10',
        name: NotificationJobType.SEND_BULK,
        data: {
          notificationTypeId: 'type-456',
          data: { message: 'Bulk notification' },
          targetAudience: {
            roles: ['nonexistent'],
          },
          channels: ['email'],
        },
      } as Job<BulkNotificationJobData>;

      notificationService.getTargetUsers.mockResolvedValue([]);

      const result = await processor.process(job);

      expect(result.success).toBe(true);
      expect(result.data.totalUsers).toBe(0);
      expect(result.data.successCount).toBe(0);
      expect(result.data.failureCount).toBe(0);
    });
  });
});
