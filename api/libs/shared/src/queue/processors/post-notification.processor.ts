import { Processor } from '@nestjs/bullmq'; // Ensure Process is imported
import { Job } from 'bullmq';

import { PostNotificationService } from '@/posts/services/post-notification.service';
import { PostNotificationJob } from '@/posts/dto/post-notification-job.dto';
@Processor('post-notification')
export class PostNotificationProcessor {
  constructor(
    private readonly postNotificationService: PostNotificationService,
  ) {}

  async process(job: Job<PostNotificationJob>): Promise<any> {
    if (job.name === 'send-post-notification') {
      const { postId, title, description, type, isGlobal, club_id } = job.data;
      return this.postNotificationService.sendPostNotification({
        postId,
        title,
        description,
        postType: type ?? 'general',
        isGlobal: isGlobal ?? false,
        clubId: club_id ?? undefined,
      });
    }
  }
}
