import { Test, TestingModule } from '@nestjs/testing';
import { LeaderboardProcessor } from './leaderboard.processor';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { RedisService } from '@app/shared/redis/redis.service';
import { Job } from 'bullmq';

describe('LeaderboardProcessor', () => {
  let processor: LeaderboardProcessor;
  let drizzleService: jest.Mocked<DrizzleService>;
  let redisService: jest.Mocked<RedisService>;

  const mockDrizzleService = {
    db: {
      execute: jest.fn(),
    },
  };

  const mockRedisClient = {
    ttl: jest.fn(),
    pipeline: jest.fn(),
    zincrby: jest.fn(),
    expire: jest.fn(),
    del: jest.fn(),
    zadd: jest.fn(),
    exec: jest.fn(),
  };

  const mockRedisService = {
    client: mockRedisClient,
    isConnected: true,
    isHealthy: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LeaderboardProcessor,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: RedisService,
          useValue: mockRedisService,
        },
      ],
    }).compile();

    processor = module.get<LeaderboardProcessor>(LeaderboardProcessor);
    drizzleService = module.get<DrizzleService>(
      DrizzleService,
    ) as jest.Mocked<DrizzleService>;
    redisService = module.get<RedisService>(
      RedisService,
    ) as jest.Mocked<RedisService>;

    // Setup pipeline mock
    const mockPipeline = {
      zincrby: jest.fn().mockReturnThis(),
      expire: jest.fn().mockReturnThis(),
      exec: jest.fn().mockResolvedValue([]),
    };
    mockRedisClient.pipeline.mockReturnValue(mockPipeline);

    jest.clearAllMocks();
  });

  describe('process', () => {
    it('should be defined', () => {
      expect(processor).toBeDefined();
    });

    it('should process refresh-materialized-views job successfully', async () => {
      const job = {
        id: 'test-job-1',
        name: 'refresh-materialized-views',
        data: {},
      } as Job;

      // Mock database execution for materialized view refresh
      // First 10 calls are REFRESH operations, next 10 are SELECT operations
      mockDrizzleService.db.execute
        .mockResolvedValueOnce(undefined) // REFRESH leaderboard_day
        .mockResolvedValueOnce(undefined) // REFRESH leaderboard_week
        .mockResolvedValueOnce(undefined) // REFRESH leaderboard_month
        .mockResolvedValueOnce(undefined) // REFRESH leaderboard_year
        .mockResolvedValueOnce(undefined) // REFRESH leaderboard_all_time
        .mockResolvedValueOnce(undefined) // REFRESH leaderboard_current_quarter
        .mockResolvedValueOnce(undefined) // REFRESH leaderboard_first_quarter
        .mockResolvedValueOnce(undefined) // REFRESH leaderboard_second_quarter
        .mockResolvedValueOnce(undefined) // REFRESH leaderboard_third_quarter
        .mockResolvedValueOnce(undefined) // REFRESH leaderboard_fourth_quarter
        .mockResolvedValue({
          rows: [
            { student_id: 'student-1', total_score: 100 },
            { student_id: 'student-2', total_score: 90 },
          ],
        }); // SELECT queries for syncing

      mockRedisClient.del.mockResolvedValue(1);
      mockRedisClient.zadd.mockResolvedValue(2);
      mockRedisClient.expire.mockResolvedValue(1);

      await processor.process(job);

      expect(mockDrizzleService.db.execute).toHaveBeenCalledTimes(20); // 10 REFRESH + 10 SELECT
      expect(mockRedisClient.del).toHaveBeenCalledTimes(10);
      expect(mockRedisClient.zadd).toHaveBeenCalledTimes(10);
      expect(mockRedisClient.expire).toHaveBeenCalledTimes(10);
    });

    it('should process update-realtime-leaderboard job successfully', async () => {
      const job = {
        id: 'test-job-2',
        name: 'update-realtime-leaderboard',
        data: {
          student_id: 'student-123',
          points: 50,
        },
      } as Job;

      // Mock Redis health checks
      mockRedisService.isHealthy.mockResolvedValue(true);

      mockRedisClient.ttl.mockResolvedValue(3600); // 1 hour remaining

      const mockPipeline = {
        zincrby: jest.fn().mockReturnThis(),
        expire: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([]),
      };
      mockRedisClient.pipeline.mockReturnValue(mockPipeline);

      await processor.process(job);

      expect(mockRedisService.isHealthy).toHaveBeenCalled();
      expect(mockRedisClient.ttl).toHaveBeenCalled();
      expect(mockRedisClient.pipeline).toHaveBeenCalled();
      expect(mockPipeline.zincrby).toHaveBeenCalledWith(
        expect.stringContaining('leaderboard:realtime'),
        50,
        'student-123',
      );
    });

    it('should handle update-realtime-leaderboard with TTL refresh needed', async () => {
      const job = {
        id: 'test-job-3',
        name: 'update-realtime-leaderboard',
        data: {
          student_id: 'student-123',
          points: 25,
        },
      } as Job;

      // Mock Redis health checks
      mockRedisService.isHealthy.mockResolvedValue(true);

      mockRedisClient.ttl.mockResolvedValue(-1); // No expiration set

      const mockPipeline = {
        zincrby: jest.fn().mockReturnThis(),
        expire: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([]),
      };
      mockRedisClient.pipeline.mockReturnValue(mockPipeline);

      await processor.process(job);

      expect(mockPipeline.expire).toHaveBeenCalled();
    });

    it('should handle update-realtime-leaderboard with key not existing', async () => {
      const job = {
        id: 'test-job-4',
        name: 'update-realtime-leaderboard',
        data: {
          student_id: 'student-123',
          points: 75,
        },
      } as Job;

      // Mock Redis health checks
      mockRedisService.isHealthy.mockResolvedValue(true);

      mockRedisClient.ttl.mockResolvedValue(-2); // Key doesn't exist

      const mockPipeline = {
        zincrby: jest.fn().mockReturnThis(),
        expire: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([]),
      };
      mockRedisClient.pipeline.mockReturnValue(mockPipeline);

      await processor.process(job);

      expect(mockPipeline.expire).toHaveBeenCalled();
    });

    it('should handle refresh-materialized-views with database error', async () => {
      const job = {
        id: 'test-job-5',
        name: 'refresh-materialized-views',
        data: {},
      } as Job;

      const error = new Error('Database connection failed');
      mockDrizzleService.db.execute.mockRejectedValue(error);

      await expect(processor.process(job)).rejects.toThrow(
        'Database connection failed',
      );
    });

    it('should handle update-realtime-leaderboard with Redis error', async () => {
      const job = {
        id: 'test-job-6',
        name: 'update-realtime-leaderboard',
        data: {
          student_id: 'student-123',
          points: 30,
        },
      } as Job;

      // Mock Redis health checks
      mockRedisService.isHealthy.mockResolvedValue(true);

      const error = new Error('Redis connection failed');
      mockRedisClient.ttl.mockRejectedValue(error);

      // The method catches errors and doesn't rethrow them
      await processor.process(job);

      expect(mockRedisClient.ttl).toHaveBeenCalled();
    });

    it('should warn for unknown job type', async () => {
      const job = {
        id: 'test-job-7',
        name: 'unknown-job-type',
        data: {},
      } as Job;

      const warnSpy = jest
        .spyOn(processor['logger'], 'warn')
        .mockImplementation();

      await processor.process(job);

      expect(warnSpy).toHaveBeenCalledWith(
        'Unknown job type: unknown-job-type',
      );
      warnSpy.mockRestore();
    });

    it('should handle materialized view refresh with empty results', async () => {
      const job = {
        id: 'test-job-8',
        name: 'refresh-materialized-views',
        data: {},
      } as Job;

      // Mock empty results for all queries
      mockDrizzleService.db.execute.mockResolvedValue({ rows: [] });

      await processor.process(job);

      expect(mockDrizzleService.db.execute).toHaveBeenCalledTimes(20);
      // Should not call zadd when no data
      expect(mockRedisClient.zadd).not.toHaveBeenCalled();
    });

    it('should handle pipeline execution errors gracefully', async () => {
      const job = {
        id: 'test-job-9',
        name: 'update-realtime-leaderboard',
        data: {
          student_id: 'student-123',
          points: 40,
        },
      } as Job;

      // Mock Redis health checks
      mockRedisService.isHealthy.mockResolvedValue(true);

      mockRedisClient.ttl.mockResolvedValue(3600);

      const mockPipeline = {
        zincrby: jest.fn().mockReturnThis(),
        expire: jest.fn().mockReturnThis(),
        exec: jest
          .fn()
          .mockRejectedValue(new Error('Pipeline execution failed')),
      };
      mockRedisClient.pipeline.mockReturnValue(mockPipeline);

      // The method catches errors and doesn't rethrow them
      await processor.process(job);

      expect(mockPipeline.exec).toHaveBeenCalled();
    });

    it('should handle different period types correctly', async () => {
      const job = {
        id: 'test-job-periods',
        name: 'update-realtime-leaderboard',
        data: {
          student_id: 'student-123',
          points: 10,
        },
      } as Job;

      // Mock Redis health checks
      mockRedisService.isHealthy.mockResolvedValue(true);

      mockRedisClient.ttl.mockResolvedValue(3600);

      const mockPipeline = {
        zincrby: jest.fn().mockReturnThis(),
        expire: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([]),
      };
      mockRedisClient.pipeline.mockReturnValue(mockPipeline);

      await processor.process(job);

      // The implementation processes all periods, so we just verify it was called
      expect(mockPipeline.zincrby).toHaveBeenCalledWith(
        expect.stringContaining('leaderboard:realtime'),
        10,
        'student-123',
      );
    });
  });
});
