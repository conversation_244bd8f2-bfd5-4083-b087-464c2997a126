import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { RedisService } from '@app/shared/redis/redis.service';
import { student_profiles } from '@/db/schema/student_profile';
import { eq } from 'drizzle-orm';

@Processor('general')
export class LeaderboardProcessor extends WorkerHost {
  private readonly logger = new Logger(LeaderboardProcessor.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly redisService: RedisService,
  ) {
    super();
  }

  async process(job: Job): Promise<void> {
    switch (job.name) {
      case 'refresh-materialized-views':
        await this.refreshMaterializedViews();
        break;
      case 'update-realtime-leaderboard':
        await this.updateRealtimeLeaderboard(job);
        break;
      default:
        this.logger.warn(`Unknown job type: ${job.name}`);
    }
  }

  /**
   * Refresh materialized views and sync with Redis
   */
  private async refreshMaterializedViews(): Promise<void> {
    try {
      this.logger.log('Starting materialized view refresh...');

      // Refresh materialized views
      await Promise.all([
        this.drizzle.db.execute(`REFRESH MATERIALIZED VIEW leaderboard_day`),
        this.drizzle.db.execute(`REFRESH MATERIALIZED VIEW leaderboard_week`),
        this.drizzle.db.execute(`REFRESH MATERIALIZED VIEW leaderboard_month`),
        this.drizzle.db.execute(`REFRESH MATERIALIZED VIEW leaderboard_year`),
        this.drizzle.db.execute(
          `REFRESH MATERIALIZED VIEW leaderboard_all_time`,
        ),
        this.drizzle.db.execute(
          `REFRESH MATERIALIZED VIEW leaderboard_current_quarter`,
        ),
        this.drizzle.db.execute(
          `REFRESH MATERIALIZED VIEW leaderboard_first_quarter`,
        ),
        this.drizzle.db.execute(
          `REFRESH MATERIALIZED VIEW leaderboard_second_quarter`,
        ),
        this.drizzle.db.execute(
          `REFRESH MATERIALIZED VIEW leaderboard_third_quarter`,
        ),
        this.drizzle.db.execute(
          `REFRESH MATERIALIZED VIEW leaderboard_fourth_quarter`,
        ),
      ]);

      // Sync Redis leaderboards with updated materialized views
      await this.syncRedisWithMaterializedViews();

      this.logger.log('Successfully refreshed materialized views');
    } catch (error) {
      this.logger.error('Failed to refresh materialized views', error);
      throw error;
    }
  }

  /**
   * Update real-time leaderboard with new points
   * Optimized for performance with parallel operations and error resilience
   */
  private async updateRealtimeLeaderboard(job: Job): Promise<void> {
    try {
      const { student_id, points } = job.data;

      this.logger.debug(
        `Processing real-time leaderboard update for student ${student_id}`,
      );

      // Performance optimization: Check if Redis is available before proceeding
      if (!this.redisService.isConnected) {
        this.logger.warn(
          'Redis not connected, skipping real-time leaderboard update',
          { student_id, points, job_id: job.id },
        );
        return;
      }

      // Additional health check with timeout
      try {
        const isHealthy = await Promise.race([
          this.redisService.isHealthy(),
          new Promise<boolean>((_, reject) =>
            setTimeout(() => reject(new Error('Health check timeout')), 2000),
          ),
        ]);

        if (!isHealthy) {
          this.logger.warn(
            'Redis health check failed, skipping leaderboard update',
            { student_id, points, job_id: job.id },
          );
          return;
        }
      } catch (healthError) {
        this.logger.warn(
          'Redis health check error, skipping leaderboard update',
          {
            student_id,
            points,
            job_id: job.id,
            error:
              healthError instanceof Error
                ? healthError.message
                : 'Unknown error',
          },
        );
        return;
      }

      // Get current period keys for leaderboard updates
      const periods = this.getCurrentPeriods();
      const leaderboardKeyPrefix = 'leaderboard:realtime';

      // Performance optimization: Use Promise.allSettled for parallel operations
      // This ensures one failing Redis operation doesn't break others
      const updatePromises = periods.map(async (period) => {
        const leaderboardKey = `${leaderboardKeyPrefix}:${period}`;

        try {
          // Check current TTL before updating (optimized approach)
          const currentTtl = await this.redisService.client.ttl(leaderboardKey);

          // Use pipeline for atomic operations (better performance)
          const pipeline = this.redisService.client.pipeline();

          // Increment student's score
          pipeline.zincrby(leaderboardKey, points, student_id);

          // Only set TTL if key doesn't exist or needs refresh
          // TTL = -1 means no expiration, TTL = -2 means key doesn't exist
          if (
            currentTtl === -1 ||
            currentTtl === -2 ||
            this.shouldRefreshTTL(period, currentTtl)
          ) {
            const ttl = this.getPeriodTTL(period);
            pipeline.expire(leaderboardKey, ttl);
          }

          // Execute pipeline atomically
          await pipeline.exec();

          return { period, success: true };
        } catch (error) {
          this.logger.warn(
            `Failed to update leaderboard for period ${period}:`,
            error,
          );
          return { period, success: false, error };
        }
      });

      // Execute all updates in parallel
      const results = await Promise.allSettled(updatePromises);

      // Log results for monitoring
      const successful = results.filter(
        (result) => result.status === 'fulfilled' && result.value.success,
      ).length;

      const failed = results.length - successful;

      if (failed > 0) {
        this.logger.warn(
          `Leaderboard update partial success: ${successful}/${results.length} periods updated for student ${student_id}`,
        );
      } else {
        this.logger.debug(
          `Successfully updated ${successful} leaderboard periods for student ${student_id} with ${points} points`,
        );
      }

      // Performance optimization: Don't block on user detail caching
      // Cache user details asynchronously (fire and forget)
      this.cacheUserDetailsAsync(student_id);
    } catch (error) {
      this.logger.error('Failed to update real-time leaderboard', error);
      // Don't rethrow - this is a background job, shouldn't fail the main flow
    }
  }

  /**
   * Get current period identifiers for leaderboard updates
   */
  private getCurrentPeriods(): string[] {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const day = now.getDate();
    const week = this.getWeekOfYear(now);

    return [
      `day:${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`,
      `week:${year}-${week}`,
      `month:${year}-${month}`,
      `quarter:${year}-${Math.ceil(month / 3)}`,
      `all-time`,
      `current-quarter`,
      `first-quarter`,
      `second-quarter`,
      `third-quarter`,
      `fourth-quarter`,
      `year:${year}`,
    ];
  }

  /**
   * Get TTL in seconds for different periods
   */
  private getPeriodTTL(period: string): number {
    if (period.startsWith('day:')) return 86400; // 24 hours
    if (period.startsWith('week:')) return 604800; // 7 days
    if (period.startsWith('month:')) return 2592000; // 30 days
    if (period.startsWith('year:')) return 31536000; // 365 days
    return 86400; // Default 24 hours
  }

  /**
   * Determine if TTL should be refreshed based on remaining time
   * Only refresh if TTL has dropped below 10% of original duration
   * @param period The period string (e.g., 'day:2024-06-16')
   * @param currentTtl Current TTL in seconds
   * @returns true if TTL should be refreshed
   */
  private shouldRefreshTTL(period: string, currentTtl: number): boolean {
    const originalTtl = this.getPeriodTTL(period);
    const refreshThreshold = originalTtl * 0.1; // 10% threshold

    // Refresh if current TTL is less than 10% of original
    // This prevents constant TTL resets while ensuring data doesn't expire prematurely
    return currentTtl > 0 && currentTtl < refreshThreshold;
  }

  /**
   * Get week of year
   */
  private getWeekOfYear(date: Date): number {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear =
      (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  }

  /**
   * Cache user details asynchronously (fire and forget)
   * Performance optimization: Don't block the main update flow
   */
  private cacheUserDetailsAsync(student_id: string): void {
    // Fire and forget - don't await this operation
    this.cacheUserDetails(student_id).catch((error) => {
      this.logger.debug(
        `Failed to cache user details for ${student_id}:`,
        error,
      );
    });
  }

  /**
   * Cache user details for leaderboard display
   */
  private async cacheUserDetails(student_id: string): Promise<void> {
    const cacheKey = `user:details:${student_id}`;

    try {
      // Check if already cached (avoid unnecessary DB query)
      const exists = await this.redisService.client.exists(cacheKey);
      if (exists) {
        return;
      }

      // Fetch minimal user details from database using Drizzle ORM
      const studentProfile =
        await this.drizzle.db.query.student_profiles.findFirst({
          where: eq(student_profiles.id, student_id),
          with: {
            user: {
              columns: {
                profile_pic_url: true,
              },
            },
            institution: {
              columns: {
                name: true,
              },
            },
          },
          columns: {
            first_name: true,
            last_name: true,
          },
        });

      if (studentProfile) {
        const userDetails = {
          first_name: studentProfile.first_name,
          last_name: studentProfile.last_name,
          profile_pic_url: studentProfile.user?.profile_pic_url,
          institution_name: studentProfile.institution?.name,
        };

        // Cache for 1 hour with exponential backoff on failure
        await this.redisService.client.setex(
          cacheKey,
          3600,
          JSON.stringify(userDetails),
        );
      }
    } catch (error) {
      // Don't throw - this is a nice-to-have optimization
      this.logger.debug(
        `Failed to cache user details for ${student_id}:`,
        error,
      );
    }
  }

  /**
   * Sync Redis leaderboards with materialized views
   */
  private async syncRedisWithMaterializedViews(): Promise<void> {
    try {
      const periods = [
        'day',
        'week',
        'month',
        'year',
        'all-time',
        'current-quarter',
        'first-quarter',
        'second-quarter',
        'third-quarter',
        'fourth-quarter',
      ];
      const leaderboardKeyPrefix = 'leaderboard:realtime';

      for (const period of periods) {
        const viewName = `leaderboard_${period}`;
        const currentPeriodKey = this.getCurrentPeriodKey(period);
        const leaderboardKey = `${leaderboardKeyPrefix}:${currentPeriodKey}`;

        // Clear existing Redis data
        await this.redisService.client.del(leaderboardKey);

        // Fetch from materialized view
        const result = await this.drizzle.db.execute(
          `SELECT student_id, total_score FROM ${viewName} ORDER BY total_score DESC LIMIT 1000`,
        );

        if (result.rows.length > 0) {
          // Bulk insert into Redis sorted set
          const members: Array<number | string> = [];
          result.rows.forEach((row: any) => {
            members.push(row.total_score, row.student_id);
          });

          await this.redisService.client.zadd(leaderboardKey, ...members);

          // Set TTL
          const ttl = this.getPeriodTTL(currentPeriodKey);
          await this.redisService.client.expire(leaderboardKey, ttl);
        }

        this.logger.debug(
          `Synced ${result.rows.length} entries for ${period} leaderboard`,
        );
      }
    } catch (error) {
      this.logger.error('Failed to sync Redis with materialized views', error);
      throw error;
    }
  }

  /**
   * Get current period key for a given period type
   */
  private getCurrentPeriodKey(period: string): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const day = now.getDate();
    const week = this.getWeekOfYear(now);

    switch (period) {
      case 'day':
        return `day:${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
      case 'week':
        return `week:${year}-${week}`;
      case 'month':
        return `month:${year}-${month}`;
      case 'year':
        return `year:${year}`;
      case 'all-time':
        return `all-time`;
      case 'current-quarter':
        return `current-quarter`;
      case 'first-quarter':
        return `first-quarter`;
      case 'second-quarter':
        return `second-quarter`;
      case 'third-quarter':
        return `third-quarter`;
      case 'fourth-quarter':
        return `fourth-quarter`;
      default:
        return `day:${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
    }
  }
}
