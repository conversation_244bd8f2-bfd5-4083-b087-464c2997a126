import { Test, TestingModule } from '@nestjs/testing';
import { PostNotificationProcessor } from './post-notification.processor';
import { PostNotificationService } from '@/posts/services/post-notification.service';
import { Job } from 'bullmq';
import { PostNotificationJob } from '@/posts/dto/post-notification-job.dto';

describe('PostNotificationProcessor', () => {
  let processor: PostNotificationProcessor;
  let postNotificationService: jest.Mocked<PostNotificationService>;

  const mockPostNotificationService = {
    sendPostNotification: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PostNotificationProcessor,
        {
          provide: PostNotificationService,
          useValue: mockPostNotificationService,
        },
      ],
    }).compile();

    processor = module.get<PostNotificationProcessor>(
      PostNotificationProcessor,
    );
    postNotificationService = module.get<PostNotificationService>(
      PostNotificationService,
    ) as jest.Mocked<PostNotificationService>;
    jest.clearAllMocks();
  });

  describe('process', () => {
    it('should be defined', () => {
      expect(processor).toBeDefined();
    });

    it('should process send-post-notification job successfully', async () => {
      const job = {
        id: 'test-job-1',
        name: 'send-post-notification',
        data: {
          postId: 'post-123',
          title: 'New Post Available',
          description: 'Check out this new post!',
          type: 'announcement',
          isGlobal: true,
          club_id: null,
        },
      } as Job<PostNotificationJob>;

      const mockResult = {
        success: true,
        notificationsSent: 25,
        message: 'Post notification sent successfully',
      };

      postNotificationService.sendPostNotification.mockResolvedValue(
        mockResult,
      );

      const result = await processor.process(job);

      expect(postNotificationService.sendPostNotification).toHaveBeenCalledWith(
        {
          postId: 'post-123',
          title: 'New Post Available',
          description: 'Check out this new post!',
          postType: 'announcement',
          isGlobal: true,
          clubId: undefined,
        },
      );

      expect(result).toEqual(mockResult);
    });

    it('should process club-specific post notification', async () => {
      const job = {
        id: 'test-job-2',
        name: 'send-post-notification',
        data: {
          postId: 'post-456',
          title: 'Club Event Announcement',
          description: 'Join us for our upcoming event!',
          type: 'event',
          isGlobal: false,
          club_id: 'club-789',
        },
      } as Job<PostNotificationJob>;

      const mockResult = {
        success: true,
        notificationsSent: 12,
        message: 'Club post notification sent successfully',
      };

      postNotificationService.sendPostNotification.mockResolvedValue(
        mockResult,
      );

      const result = await processor.process(job);

      expect(postNotificationService.sendPostNotification).toHaveBeenCalledWith(
        {
          postId: 'post-456',
          title: 'Club Event Announcement',
          description: 'Join us for our upcoming event!',
          postType: 'event',
          isGlobal: false,
          clubId: 'club-789',
        },
      );

      expect(result).toEqual(mockResult);
    });

    it('should handle post notification with default values', async () => {
      const job = {
        id: 'test-job-3',
        name: 'send-post-notification',
        data: {
          postId: 'post-789',
          title: 'Simple Post',
          description: 'A simple post notification',
          // type, isGlobal, and club_id are undefined
        },
      } as Job<PostNotificationJob>;

      const mockResult = {
        success: true,
        notificationsSent: 5,
        message: 'Post notification sent with defaults',
      };

      postNotificationService.sendPostNotification.mockResolvedValue(
        mockResult,
      );

      const result = await processor.process(job);

      expect(postNotificationService.sendPostNotification).toHaveBeenCalledWith(
        {
          postId: 'post-789',
          title: 'Simple Post',
          description: 'A simple post notification',
          postType: 'general', // Default value
          isGlobal: false, // Default value
          clubId: undefined, // Default value
        },
      );

      expect(result).toEqual(mockResult);
    });

    it('should handle post notification service errors', async () => {
      const job = {
        id: 'test-job-4',
        name: 'send-post-notification',
        data: {
          postId: 'post-error',
          title: 'Error Post',
          description: 'This will fail',
          type: 'general',
          isGlobal: true,
        },
      } as Job<PostNotificationJob>;

      const error = new Error('Post notification service failed');
      postNotificationService.sendPostNotification.mockRejectedValue(error);

      await expect(processor.process(job)).rejects.toThrow(
        'Post notification service failed',
      );
    });

    it('should return undefined for non-matching job names', async () => {
      const job = {
        id: 'test-job-5',
        name: 'unknown-job-type',
        data: {
          postId: 'post-123',
          title: 'Test',
          description: 'Test',
        },
      } as Job<PostNotificationJob>;

      const result = await processor.process(job);

      expect(result).toBeUndefined();
      expect(
        postNotificationService.sendPostNotification,
      ).not.toHaveBeenCalled();
    });

    it('should handle missing required fields gracefully', async () => {
      const job = {
        id: 'test-job-6',
        name: 'send-post-notification',
        data: {
          // Missing postId, title, description
          type: 'general',
          isGlobal: false,
        },
      } as Job<PostNotificationJob>;

      const mockResult = {
        success: false,
        error: 'Missing required fields',
      };

      postNotificationService.sendPostNotification.mockResolvedValue(
        mockResult,
      );

      const result = await processor.process(job);

      expect(postNotificationService.sendPostNotification).toHaveBeenCalledWith(
        {
          postId: undefined,
          title: undefined,
          description: undefined,
          postType: 'general',
          isGlobal: false,
          clubId: undefined,
        },
      );

      expect(result).toEqual(mockResult);
    });

    it('should handle null values in job data', async () => {
      const job = {
        id: 'test-job-7',
        name: 'send-post-notification',
        data: {
          postId: 'post-123',
          title: 'Test Post',
          description: 'Test description',
          type: null,
          isGlobal: null,
          club_id: null,
        },
      } as Job<PostNotificationJob>;

      const mockResult = {
        success: true,
        notificationsSent: 3,
      };

      postNotificationService.sendPostNotification.mockResolvedValue(
        mockResult,
      );

      const result = await processor.process(job);

      expect(postNotificationService.sendPostNotification).toHaveBeenCalledWith(
        {
          postId: 'post-123',
          title: 'Test Post',
          description: 'Test description',
          postType: 'general', // null becomes default
          isGlobal: false, // null becomes default
          clubId: undefined, // null becomes undefined
        },
      );

      expect(result).toEqual(mockResult);
    });

    it('should handle empty string values appropriately', async () => {
      const job = {
        id: 'test-job-8',
        name: 'send-post-notification',
        data: {
          postId: 'post-123',
          title: '',
          description: '',
          type: '',
          isGlobal: false,
          club_id: '',
        },
      } as Job<PostNotificationJob>;

      const mockResult = {
        success: true,
        notificationsSent: 1,
      };

      postNotificationService.sendPostNotification.mockResolvedValue(
        mockResult,
      );

      const result = await processor.process(job);

      expect(postNotificationService.sendPostNotification).toHaveBeenCalledWith(
        {
          postId: 'post-123',
          title: '',
          description: '',
          postType: '', // Empty string is passed through, not replaced with 'general'
          isGlobal: false,
          clubId: '', // Empty string is passed through, not replaced with undefined
        },
      );

      expect(result).toEqual(mockResult);
    });

    it('should handle service returning null or undefined', async () => {
      const job = {
        id: 'test-job-9',
        name: 'send-post-notification',
        data: {
          postId: 'post-123',
          title: 'Test',
          description: 'Test',
        },
      } as Job<PostNotificationJob>;

      postNotificationService.sendPostNotification.mockResolvedValue(null);

      const result = await processor.process(job);

      expect(result).toBeNull();
    });

    it('should handle async processing correctly', async () => {
      const job = {
        id: 'test-job-10',
        name: 'send-post-notification',
        data: {
          postId: 'post-async',
          title: 'Async Test',
          description: 'Testing async processing',
          type: 'urgent',
          isGlobal: true,
        },
      } as Job<PostNotificationJob>;

      // Simulate async delay
      postNotificationService.sendPostNotification.mockImplementation(
        () =>
          new Promise((resolve) =>
            setTimeout(
              () => resolve({ success: true, notificationsSent: 10 }),
              100,
            ),
          ),
      );

      const startTime = Date.now();
      const result = await processor.process(job);
      const endTime = Date.now();

      expect(endTime - startTime).toBeGreaterThanOrEqual(100);
      expect(result).toEqual({ success: true, notificationsSent: 10 });
    });
  });
});
