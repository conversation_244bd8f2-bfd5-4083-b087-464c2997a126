import { Test, TestingModule } from '@nestjs/testing';
import { EmailProcessor } from './email.processor';
import { EmailService } from '@/mail/email.service';
import { Job } from 'bullmq';
import { EmailJobType } from '../queue.constants';
import type { SingleEmailJobData, BulkEmailJobData } from '../queue.types';

describe('EmailProcessor', () => {
  let processor: EmailProcessor;
  let emailService: jest.Mocked<EmailService>;

  const mockEmailService = {
    sendCustomEmail: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailProcessor,
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
      ],
    }).compile();

    processor = module.get<EmailProcessor>(EmailProcessor);
    emailService = module.get<EmailService>(
      EmailService,
    ) as jest.Mocked<EmailService>;
    jest.clearAllMocks();
  });

  describe('process', () => {
    it('should be defined', () => {
      expect(processor).toBeDefined();
    });

    it('should force EMAIL_TOGGLE to ON when disabled', async () => {
      const originalToggle = process.env.EMAIL_TOGGLE;
      process.env.EMAIL_TOGGLE = 'OFF';

      const job = {
        id: 'test-job-1',
        name: EmailJobType.SEND_SINGLE,
        data: {
          to: '<EMAIL>',
          subject: 'Test Subject',
          template: 'test-template',
          context: { name: 'Test User' },
        },
      } as Job<SingleEmailJobData>;

      emailService.sendCustomEmail.mockResolvedValue(undefined);

      await processor.process(job);

      expect(process.env.EMAIL_TOGGLE).toBe('ON');

      // Restore original value
      process.env.EMAIL_TOGGLE = originalToggle;
    });

    it('should process single email job successfully', async () => {
      const job = {
        id: 'test-job-1',
        name: EmailJobType.SEND_SINGLE,
        data: {
          to: '<EMAIL>',
          subject: 'Test Subject',
          template: 'test-template',
          context: { name: 'Test User' },
        },
      } as Job<SingleEmailJobData>;

      emailService.sendCustomEmail.mockResolvedValue(undefined);

      const result = await processor.process(job);

      expect(emailService.sendCustomEmail).toHaveBeenCalledWith({
        email: '<EMAIL>',
        subject: 'Test Subject',
        template: 'test-template',
        context: { name: 'Test User' },
        critical: true,
      });

      expect(result).toEqual({
        success: true,
        message: 'Email <NAME_EMAIL>',
        data: {
          to: '<EMAIL>',
          subject: 'Test Subject',
          template: 'test-template',
        },
      });
    });

    it('should handle single email job failure', async () => {
      const job = {
        id: 'test-job-1',
        name: EmailJobType.SEND_SINGLE,
        data: {
          to: '<EMAIL>',
          subject: 'Test Subject',
          template: 'test-template',
          context: { name: 'Test User' },
        },
      } as Job<SingleEmailJobData>;

      const error = new Error('Email service error');
      emailService.sendCustomEmail.mockRejectedValue(error);

      const result = await processor.process(job);

      expect(result).toEqual({
        success: false,
        message: 'Failed to send <NAME_EMAIL>',
        error: 'Email service error',
        data: {
          to: '<EMAIL>',
          subject: 'Test Subject',
          template: 'test-template',
        },
      });
    });

    it('should process bulk email job successfully', async () => {
      const job = {
        id: 'test-job-2',
        name: EmailJobType.SEND_BULK,
        data: {
          emails: [
            {
              to: '<EMAIL>',
              subject: 'Bulk Subject 1',
              template: 'bulk-template',
              context: { name: 'User 1' },
            },
            {
              to: '<EMAIL>',
              subject: 'Bulk Subject 2',
              template: 'bulk-template',
              context: { name: 'User 2' },
            },
          ],
          batchSize: 10,
        },
      } as Job<BulkEmailJobData>;

      emailService.sendCustomEmail.mockResolvedValue(undefined);

      const result = await processor.process(job);

      expect(emailService.sendCustomEmail).toHaveBeenCalledTimes(2);
      expect(result.success).toBe(true);
      expect(result.data.successCount).toBe(2);
      expect(result.data.failureCount).toBe(0);
    });

    it('should handle partial failures in bulk email job', async () => {
      const job = {
        id: 'test-job-3',
        name: EmailJobType.SEND_BULK,
        data: {
          emails: [
            {
              to: '<EMAIL>',
              subject: 'Bulk Subject 1',
              template: 'bulk-template',
              context: { name: 'User 1' },
            },
            {
              to: '<EMAIL>',
              subject: 'Bulk Subject 2',
              template: 'bulk-template',
              context: { name: 'User 2' },
            },
          ],
          batchSize: 10,
        },
      } as Job<BulkEmailJobData>;

      emailService.sendCustomEmail
        .mockResolvedValueOnce(undefined) // First email succeeds
        .mockRejectedValueOnce(new Error('Second email fails')); // Second email fails

      const result = await processor.process(job);

      expect(result.success).toBe(true);
      expect(result.data.successCount).toBe(1);
      expect(result.data.failureCount).toBe(1);
      expect(result.data.failures).toHaveLength(1);
      expect(result.data.failures[0].to).toBe('<EMAIL>');
      expect(result.data.failures[0].error).toBe('Second email fails');
    });

    it('should use default batch size when not specified', async () => {
      const job = {
        id: 'test-job-4',
        name: EmailJobType.SEND_BULK,
        data: {
          emails: [
            {
              to: '<EMAIL>',
              subject: 'Bulk Subject 1',
              template: 'bulk-template',
              context: { name: 'User 1' },
            },
          ],
          // No batchSize specified
        },
      } as Job<BulkEmailJobData>;

      emailService.sendCustomEmail.mockResolvedValue(undefined);

      const result = await processor.process(job);

      expect(result.success).toBe(true);
      expect(result.data.successCount).toBe(1);
    });

    it('should throw error for unknown job type', async () => {
      const job = {
        id: 'test-job-5',
        name: 'UNKNOWN_TYPE',
        data: {},
      } as Job;

      await expect(processor.process(job)).rejects.toThrow(
        'Unknown job type: UNKNOWN_TYPE',
      );
    });

    it('should handle job processing errors', async () => {
      const job = {
        id: 'test-job-6',
        name: EmailJobType.SEND_SINGLE,
        data: {
          to: '<EMAIL>',
          subject: 'Test Subject',
          template: 'test-template',
          context: { name: 'Test User' },
        },
      } as Job<SingleEmailJobData>;

      // Mock a processing error
      emailService.sendCustomEmail.mockRejectedValue(
        new Error('Processing error'),
      );

      const result = await processor.process(job);

      expect(result).toEqual({
        success: false,
        message: 'Failed to send <NAME_EMAIL>',
        error: 'Processing error',
        data: {
          to: '<EMAIL>',
          subject: 'Test Subject',
          template: 'test-template',
        },
      });
    });
  });

  describe('development mode logging', () => {
    it('should log detailed information in development mode', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      const logSpy = jest
        .spyOn(processor['logger'], 'log')
        .mockImplementation();

      const job = {
        id: 'test-job-dev',
        name: EmailJobType.SEND_SINGLE,
        data: {
          to: '<EMAIL>',
          subject: 'Test Subject',
          template: 'test-template',
          context: { name: 'Test User' },
        },
      } as Job<SingleEmailJobData>;

      emailService.sendCustomEmail.mockResolvedValue(undefined);

      await processor.process(job);

      expect(logSpy).toHaveBeenCalledWith(
        expect.stringContaining('Processing email job test-job-dev'),
      );

      logSpy.mockRestore();
      process.env.NODE_ENV = originalEnv;
    });
  });
});
