import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { QueueName } from './queue.constants';
import { InAppProcessor } from './processors/in-app.processor';
import { EnhancedNotificationModule } from '../enhanced-notification/enhanced-notification.module';

@Module({
  imports: [
    BullModule.registerQueue({
      name: QueueName.IN_APP,
      prefix: 'bull',
      defaultJobOptions: {
        attempts: 5,
        backoff: {
          type: 'exponential',
          delay: 1000,
        },
        removeOnComplete: {
          age: 7 * 24 * 60 * 60,
          count: 1000,
        },
        removeOnFail: {
          age: 14 * 24 * 60 * 60,
        },
      },
    }),
    EnhancedNotificationModule,
  ],
  providers: [InAppProcessor],
})
export class InAppQueueModule {}
