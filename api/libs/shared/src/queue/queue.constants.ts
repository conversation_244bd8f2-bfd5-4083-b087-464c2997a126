/**
 * Queue names
 */
export enum QueueName {
  NOTIFICATION = 'notification',
  EMAIL = 'email',
  PUSH = 'push',
  GENERAL = 'general',
  POST_NOTIFICATION = 'post-notification',
  IN_APP = 'in-app',
  UPLOAD = 'upload',
}

/**
 * Job types for each queue
 */
export enum NotificationJobType {
  SEND_SINGLE = 'send-single-notification',
  SEND_BULK = 'send-bulk-notification',
  PROCESS_SCHEDULED = 'process-scheduled-notifications',
}

export enum EmailJobType {
  SEND_SINGLE = 'send-single-email',
  SEND_BULK = 'send-bulk-email',
}

export enum PushJobType {
  SEND_SINGLE = 'send-single-push',
  SEND_BULK = 'send-bulk-push',
}

export enum GeneralJobType {
  PROCESS_TASK = 'process-task',
  ACADEMIC_EMAIL_REMINDER = 'academic-email-reminder',
  BULK_DELETE_USERS = 'bulk-delete-users',
}

export enum UploadJobType {
  UPLOAD_FILE = 'upload-file',
}

export enum InAppJobType {
  SEND_SINGLE = 'send-single-in-app',
  SEND_BULK = 'send-bulk-in-app',
}

/**
 * Default job options
 */
export const DEFAULT_JOB_OPTIONS = {
  attempts: 3,
  backoff: {
    type: 'exponential',
    delay: 1000,
  },
  removeOnComplete: true,
};

/**
 * Default concurrency for processors
 * Increased to handle 30,000+ users efficiently
 * Reduced from 20 to 10 to prevent Redis command timeouts
 * by limiting the number of concurrent connections
 */
export const DEFAULT_CONCURRENCY = 10;
