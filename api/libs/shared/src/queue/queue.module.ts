import { DynamicModule, Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { QueueService } from './queue.service';
import { EmailProcessor } from './processors/email.processor';
import { PushProcessor } from './processors/push.processor';
import { GeneralProcessor } from './processors/general.processor';
import { QueueName } from './queue.constants';
import { EmailModule } from '@/mail/email.module';
import { NotificationModule } from '../notification/notification.module';
import { UploadProcessor } from './processors/upload.processor';
import { UploadModule } from '@/upload/upload.module';
import { RepositoriesModule } from '@/repositories/repositories.module';
import { UploadQueueModule } from '../upload/upload.module';
import { ConfigurableDatabaseModule } from '@app/shared/drizzle/drizzle.module-definition';
import { RedisService } from '../redis/redis.service';

@Module({})
export class QueueModule {
  static register(): DynamicModule {
    return {
      module: QueueModule,
      imports: [
        BullModule.forRootAsync({
          inject: [RedisService],
          useFactory: (redisService: RedisService) => {
            return redisService.createBullMQConfig();
          },
        }),
        BullModule.registerQueue(
          {
            name: QueueName.NOTIFICATION,
          },
          {
            name: QueueName.EMAIL,
          },
          {
            name: QueueName.PUSH,
          },
          {
            name: QueueName.GENERAL,
          },
          {
            name: QueueName.IN_APP,
          },
          {
            name: QueueName.UPLOAD,
            defaultJobOptions: {
              attempts: 10,
              backoff: {
                type: 'exponential',
                delay: 2000,
              },
              removeOnComplete: {
                age: 14 * 24 * 60 * 60,
                count: 1000,
              },
              removeOnFail: {
                age: 30 * 24 * 60 * 60,
              },
            },
          },
        ),
        EmailModule,
        NotificationModule,
        UploadModule,
        RepositoriesModule,
        UploadQueueModule,
        ConfigurableDatabaseModule.register({
          connectionString:
            process.env.DATABASE_URL ??
            'postgresql://postgres:postgres@localhost:5432/postgres',
        }),
      ],
      providers: [
        QueueService,
        EmailProcessor,
        PushProcessor,
        GeneralProcessor,
        UploadProcessor,
      ],
      exports: [QueueService],
    };
  }

  static forFeature(): DynamicModule {
    return {
      module: QueueModule,
      imports: [
        BullModule.registerQueue(
          { name: QueueName.NOTIFICATION },
          { name: QueueName.EMAIL },
          { name: QueueName.PUSH },
          { name: QueueName.GENERAL },
          { name: QueueName.IN_APP },
        ),
      ],
      providers: [QueueService],
      exports: [QueueService],
    };
  }
}
