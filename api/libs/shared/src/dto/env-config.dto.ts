import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const envConfigSchema = z.object({
  DATABASE_URL: z.string().url(),
  PORT: z.coerce.number().positive(),
  NODE_ENV: z.union([
    z.literal('development'),
    z.literal('production'),
    z.literal('staging'),
    z.literal('testing'),
    z.literal('local'),
  ]),
  SMTP_USERNAME: z.string(),
  SMTP_PASSWORD: z.string(),
  SMTP_ENDPOINT: z.string(),
  SMTP_PORT: z.coerce.number().positive(),
  SENDER_EMAIL: z.string(),
  APP_NAME: z.string(),
  BACKEND_URL: z.string().url(),
  FRONTEND_URL: z.string().url(),
  ACCESS_TOKEN_SECRET: z.string(),
  ACCESS_TOKEN_EXPIRY: z.string(),
  REFRESH_TOKEN_SECRET: z.string(),
  REFRESH_TOKEN_EXPIRY: z.string(),
  MAGIC_LINK_EXPIRY: z.string(),
  OTP_HASH_SECRET: z.string(),
  API_DOC_USERNAME: z.string(),
  API_DOC_PASSWORD: z.string(),
  SENTRY_DSN: z.string(),
  OTP_EXPIRY_TIME: z.coerce.number().positive(),
  SUPER_ADMIN_EMAIL: z.string().email(),
  SUPER_ADMIN_ROLE: z.string(),
  SUPER_ADMIN_STATE: z.string(),
  AWS_ACCESS_KEY_ID: z.string(),
  AWS_REGION: z.string(),
  AWS_SECRET_ACCESS_KEY: z.string(),
  AWS_BUCKET_NAME: z.string(),
  AWS_ENDPOINT: z.string(),
  MAX_FILE_SIZE: z.coerce.number().positive(),
  THROTTLE_TTL: z.coerce.number().positive(),
  THROTTLE_LIMIT: z.coerce.number().positive(),
  FIREBASE_SERVICE_ACCOUNT_BASE64: z.string().base64().optional().default(''),
  REDIS_HOST: z.string(),
  REDIS_PORT: z.coerce.number(),
  REDIS_PASSWORD: z.string().optional(),
  REDIS_USERNAME: z.string().optional(),
  REDIS_DB: z.coerce.number().optional(),
  REDIS_TLS: z.coerce.boolean().optional(),
  REDIS_MODE: z.enum(['single', 'cluster', 'auto']).default('single'),
  REDIS_CLUSTER_NODES: z.string().optional(),
  REDIS_HEALTHCHECK_INTERVAL_MS: z.coerce.number().positive().default(30000),
  REDIS_LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  REDIS_CONNECTION_TIMEOUT_MS: z.coerce.number().positive().default(20000),
  REDIS_COMMAND_TIMEOUT_MS: z.coerce.number().positive().default(20000),

  EMAIL_ENVIRONMENT: z
    .enum(['production', 'development', 'staging'])
    .default('development'),
  EMAIL_TOGGLE: z.enum(['ON', 'OFF']).default('ON'),

  // Cache configuration
  CACHE_ENABLED: z.enum(['true', 'false']).default('true'),
  CACHE_VERSION: z.string().default('1'),
  CACHE_NAMESPACE: z.string().default('app'),
  CACHE_DEFAULT_TTL: z.coerce.number().positive().default(3600),
  CACHE_WARMUP_ENABLED: z.enum(['true', 'false']).default('true'),

  // Queue configuration
  QUEUE_CONCURRENCY: z.coerce.number().positive().default(5),
  QUEUE_ATTEMPTS: z.coerce.number().positive().default(3),
  QUEUE_BACKOFF_DELAY: z.coerce.number().positive().default(1000),
  QUEUE_REMOVE_ON_COMPLETE: z.coerce.boolean().default(true),
  QUEUE_REMOVE_ON_FAIL: z.coerce.boolean().default(false),

  // Bull Board UI configuration
  BULL_BOARD_USERNAME: z.string().default('admin'),
  BULL_BOARD_PASSWORD: z.string().optional().default('admin'),

  // Admin emails configuration (comma-separated string that gets transformed to array)
  ADMIN_EMAIL: z
    .string()
    .transform((val) => {
      // Handle comma-separated string from environment
      if (typeof val === 'string') {
        return val.split(',').map((email) => email.trim());
      }
      return val;
    })
    .pipe(z.array(z.string().email()))
    .default(
      '<EMAIL>,<EMAIL>,<EMAIL>',
    ),
});

export class EnvConfig extends createZodDto(envConfigSchema) {}
