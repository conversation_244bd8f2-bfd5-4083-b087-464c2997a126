export const SkillControllerMessages = {
  SkillNotFound: 'Skill not found',
  SkillCategoryNotFound: 'Skill category not found',
  CannotCreateSkill:
    'An error occurred while creating the skill. Please try again later.',
  CannotGetSkills:
    'An error occurred while fetching skills. Please try again later.',
  CannotCreateSkillCategory:
    'An error occurred while creating the skill category. Please try again later.',
  CannotGetSkillCategories:
    'An error occurred while fetching skill categories. Please try again later.',
};

export const SkillServiceMessages = {
  SkillNotFound: 'Skill not found',
  SkillCategoryNotFound: (id: string) =>
    `Skill category with ID '${id}' does not exist`,
  SkillNameExists: (name: string) => `Skill with name '${name}' already exists`,
  SkillCategoryNameExists: (name: string) =>
    `Skill category with name '${name}' already exists`,
  FailedToCreateSkill: 'Failed to create skill',
  FailedToUpdateSkill: 'Failed to update skill, skill not found',
  FailedToDeleteSkill: 'Failed to delete skill, skill not found',
  FailedToCreateSkillCategory: 'Failed to create skill category',
  FailedToUpdateSkillCategory:
    'Failed to update skill category, category not found',
  FailedToDeleteSkillCategory:
    'Failed to delete skill category, category not found',
};

export const SkillRoutes = {
  // Skills
  ADD_SKILL: '',
  GET_SKILL_BY_ID: ':id',
  GET_ALL_SKILLS: '',
  GET_SKILLS_BY_CATEGORY: 'category/:categoryId',
  UPDATE_SKILL: ':id',
  DELETE_SKILL: ':id',

  // Student Skills
  ADD_STUDENT_SKILL: 'student',
  GET_STUDENT_SKILLS: 'student/:studentId',
  DELETE_STUDENT_SKILL: 'student/:studentId/skill/:skillId',
  UPDATE_STUDENT_SKILL: 'student/:studentId/skill/:skillId',

  // Skill Categories
  CATEGORIES_BASE: 'categories',
  ADD_SKILL_CATEGORY: 'categories',
  GET_SKILL_CATEGORY_BY_ID: 'categories/:id',
  GET_ALL_SKILL_CATEGORIES: 'categories',
  UPDATE_SKILL_CATEGORY: 'categories/:id',
  DELETE_SKILL_CATEGORY: 'categories/:id',
};
