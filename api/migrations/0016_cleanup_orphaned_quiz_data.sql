-- Cleanup orphaned quiz data before applying foreign key constraints
-- This migration handles quizzes that reference non-existent users

-- Step 1: Drop the existing quiz_score constraint that's causing deletion issues
ALTER TABLE "quiz_score" DROP CONSTRAINT IF EXISTS "quiz_score_quiz_id_quiz_id_fk";

-- Step 2: Make quiz.created_by nullable first
ALTER TABLE "quiz" ALTER COLUMN "created_by" DROP NOT NULL;

-- Step 3: Set created_by to NULL for quizzes that reference non-existent users
UPDATE quiz
SET created_by = NULL
WHERE created_by IS NOT NULL
  AND created_by NOT IN (SELECT id FROM users);

-- Step 4: Add the new quiz.created_by foreign key with SET NULL behavior
ALTER TABLE "quiz" ADD CONSTRAINT "quiz_created_by_users_id_fk"
FOREIGN KEY ("created_by") REFERENCES "public"."users"("id")
ON DELETE SET NULL ON UPDATE NO ACTION;

-- Step 5: Recreate the quiz_score constraint with CASCADE behavior
ALTER TABLE "quiz_score" ADD CONSTRAINT "quiz_score_quiz_id_quiz_id_fk"
FOREIGN KEY ("quiz_id") REFERENCES "public"."quiz"("id")
ON DELETE CASCADE ON UPDATE NO ACTION;
