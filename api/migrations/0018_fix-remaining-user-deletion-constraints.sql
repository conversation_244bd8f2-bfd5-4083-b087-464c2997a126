-- Fix remaining foreign key constraints for proper user deletion
-- This migration addresses the remaining issues that prevent clean user deletion

-- 1. Fix questions.created_by constraint from RESTRICT to SET NULL
-- This allows users to be deleted even if they have created questions
-- Questions will remain but with created_by set to NULL
ALTER TABLE "questions" DROP CONSTRAINT IF EXISTS "questions_created_by_users_id_fk";
--> statement-breakpoint
ALTER TABLE "questions" ADD CONSTRAINT "questions_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;

-- 2. Add missing foreign key constraint for quiz.created_by
-- Currently this field exists but has no foreign key constraint
-- Add it with SET NULL behavior to allow user deletion
ALTER TABLE "quiz" DROP CONSTRAINT IF EXISTS "quiz_created_by_users_id_fk";
--> statement-breakpoint
ALTER TABLE "quiz" ADD CONSTRAINT "quiz_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;

-- 3. Fix quiz_score.quiz_id constraint to CASCADE
-- When a quiz is deleted (due to user deletion), quiz scores should also be deleted
ALTER TABLE "quiz_score" DROP CONSTRAINT IF EXISTS "quiz_score_quiz_id_quiz_id_fk";
--> statement-breakpoint
ALTER TABLE "quiz_score" ADD CONSTRAINT "quiz_score_quiz_id_quiz_id_fk" FOREIGN KEY ("quiz_id") REFERENCES "public"."quiz"("id") ON DELETE cascade ON UPDATE no action;

-- 4. Ensure student_club_memberships properly cascade when student_profiles are deleted
-- This should already be handled by the student_profiles CASCADE, but let's make it explicit
ALTER TABLE "student_club_memberships" DROP CONSTRAINT IF EXISTS "student_club_memberships_student_id_student_profiles_id_fk";
--> statement-breakpoint
ALTER TABLE "student_club_memberships" ADD CONSTRAINT "student_club_memberships_student_id_student_profiles_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."student_profiles"("id") ON DELETE cascade ON UPDATE no action;

-- 5. Ensure raffle_participants properly cascade when student_profiles are deleted
-- This should already be handled, but let's make it explicit
ALTER TABLE "raffle_participants" DROP CONSTRAINT IF EXISTS "raffle_participants_student_id_student_profiles_id_fk";
--> statement-breakpoint
ALTER TABLE "raffle_participants" ADD CONSTRAINT "raffle_participants_student_id_student_profiles_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."student_profiles"("id") ON DELETE cascade ON UPDATE no action;

-- 6. Fix post_engagements to cascade when student_profiles are deleted
ALTER TABLE "post_engagements" DROP CONSTRAINT IF EXISTS "post_engagements_user_id_student_profiles_id_fk";
--> statement-breakpoint
ALTER TABLE "post_engagements" ADD CONSTRAINT "post_engagements_user_id_student_profiles_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."student_profiles"("id") ON DELETE cascade ON UPDATE no action;

-- 7. Ensure points_logs cascade when student_profiles are deleted
-- This should already be handled, but let's make it explicit
ALTER TABLE "points_logs" DROP CONSTRAINT IF EXISTS "points_logs_student_id_student_profiles_id_fk";
--> statement-breakpoint
ALTER TABLE "points_logs" ADD CONSTRAINT "points_logs_student_id_student_profiles_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."student_profiles"("id") ON DELETE cascade ON UPDATE no action;

-- 8. Ensure studentSkills cascade when student_profiles are deleted
ALTER TABLE "studentSkills" DROP CONSTRAINT IF EXISTS "studentSkills_student_profile_id_student_profiles_id_fk";
--> statement-breakpoint
ALTER TABLE "studentSkills" ADD CONSTRAINT "studentSkills_student_profile_id_student_profiles_id_fk" FOREIGN KEY ("student_profile_id") REFERENCES "public"."student_profiles"("id") ON DELETE cascade ON UPDATE no action;

-- 9. Fix students_careers to cascade when student_profiles are deleted
ALTER TABLE "students_careers" DROP CONSTRAINT IF EXISTS "students_careers_student_id_student_profiles_id_fk";
--> statement-breakpoint
ALTER TABLE "students_careers" ADD CONSTRAINT "students_careers_student_id_student_profiles_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."student_profiles"("id") ON DELETE cascade ON UPDATE no action;

-- Summary of changes:
-- - questions.created_by: RESTRICT → SET NULL (allows user deletion, questions remain orphaned)
-- - quiz.created_by: Added missing constraint with SET NULL
-- - quiz_score.quiz_id: NO ACTION → CASCADE (quiz scores deleted when quiz is deleted)
-- - All student_profile related tables: Ensured CASCADE behavior for proper cleanup
-- 
-- This ensures that when a user is deleted:
-- 1. Their student_profile is deleted (CASCADE)
-- 2. All records referencing student_profile are deleted (CASCADE)
-- 3. Questions and quizzes they created remain but with created_by = NULL
-- 4. No foreign key constraint violations occur
