# Stage 1: Build the application
FROM node:20-alpine AS builder

# Create app directory
WORKDIR /usr/src/app

# Install app dependencies
COPY package*.json ./
RUN npm install --save-dev @types/lodash --legacy-peer-deps --ignore-scripts

# Copy the app code and build the NestJS app
COPY . .
RUN npm run build

# Stage 2: Create the final image
FROM node:20-alpine

# Install shadow package for addgroup and adduser commands
RUN apk add --no-cache shadow

# Create a non-root user and group
RUN addgroup -S reach-api && adduser -S -G reach-api reach-api

# Create and change to the app directory
WORKDIR /usr/src/app

# Create cert directory
RUN mkdir -p /usr/src/app/cert

# Change ownership of the /app directory to the non-root user
RUN chown -R reach-api:reach-api /usr/src/app

# Copy only the build output and production dependencies from the builder stage
COPY --from=builder /usr/src/app/dist ./dist
COPY --from=builder /usr/src/app/node_modules ./node_modules
COPY --from=builder /usr/src/app/package*.json ./.
COPY --from=builder /usr/src/app/drizzle.config.ts ./.
COPY --from=builder /usr/src/app/migrations ./migrations
COPY --from=builder /usr/src/app/scripts ./scripts
COPY --from=builder /usr/src/app/tsconfig.json ./
COPY --from=builder /usr/src/app/src ./src
COPY --from=builder /usr/src/app/libs ./libs
COPY --from=builder /usr/src/app/docs ./docs

# Expose the application port
EXPOSE 3003

# Switch to the non-root user
USER reach-api

# Start the application and run commands
CMD ["sh", "-c", "npx drizzle-kit migrate && node dist/src/pre-main"]

