# Touching Student Lives Application

## About

The **Touching Student Lives** application is a robust three-tier system crafted to bridge the gap between **AmaliTech** and students in tertiary institutions. This application fosters engagement by providing students with direct access to **AmaliTech's** latest information, opportunities, and updates.

The project consists of the following components:

1. **Mobile Application**: A user-friendly interface designed for students to:
   - Stay updated with AmaliTech's news, events, and announcements.
   - Explore available opportunities such as internships, jobs, and training programs.
   - Access resources to enhance their academic and professional development.

2. **Backend System**: Powers the application's functionality, ensuring seamless interaction between users and the system's data.

3. **Administrative Dashboard**: A management interface for AmaliTech to:
   - Publish updates and manage content.
   - Monitor student engagement metrics.
   - Maintain the application efficiently.

## Features

- **Personalized User Experience**: Students can access tailored content based on their interests.
- **Real-Time Notifications**: Stay informed about new opportunities and updates.
- **Resource Center**: Access to learning materials, career guides, and training resources.
- **Feedback Mechanism**: Students can communicate with AmaliTech through the app.

## Technologies Used

- **Frontend**: Flutter for the mobile application.
- **Backend**: Node.js (or specify if different).
- **Database**: PostgreSQL (or specify if different).
- **Authentication**: Secure login with OTP or magic link.
- **Navigation**: Go Router for seamless navigation.

## Getting Started

### Prerequisites

- Flutter installed on your local machine.
- Backend and database setup details (if hosted, provide API endpoints).
- Basic understanding of the project's architecture.

### Installation

1. Clone the repository:
   `git clone https://github.com/<username>/touching-student-lives.git`

2. Navigate to the project directory:
   `cd touching-student-lives/mobile`

3. Install dependencies:
   `flutter pub get`
4. Install app on phone: `flutter install apk --release`

## Screenshots

### Home Screen
![Home Screen](mobile/assets/images/screen_shots/Screenshot_20241202_113631.png "Home Screen UI")

### Opportunities Section
![Opportunities Section](https://example.com/opportunities_section.png "Opportunities in App")

