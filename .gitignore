# General
.DS_Store
*.log
*.env
*.env.*
!*.env.example
.env

# IDEs and editors
.idea/
.vscode/
*.swp
*.swo

# Mobile (Flutter)
mobile/build/
mobile/.dart_tool/
mobile/.flutter-plugins
mobile/.flutter-plugins-dependencies
mobile/.packages
mobile/pubspec.lock

# API (NestJS)
api/dist/
api/node_modules/
api/npm-debug.log*
api/yarn-debug.log*
api/yarn-error.log*

# Web Frontend
web/build/
web/node_modules/
web/npm-debug.log*
web/yarn-debug.log*
web/yarn-error.log*

# Shared
shared/**/*.js
shared/**/*.js.map

# Tests
coverage/

# Temporary files
*.tmp
*.temp

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Rough Works
mobile/lib/config/rough_works

.qodo
